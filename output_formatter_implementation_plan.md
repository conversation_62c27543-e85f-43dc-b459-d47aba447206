# cargo-vcontract 输出格式化统一实现计划

## 概述

当前项目已经完成了错误处理的统一（第一阶段），现在需要进行第二阶段工作：输出格式化统一。该阶段的目标是创建一个集中的输出处理模块，统一各命令的输出格式，减少代码重复，并确保输出风格的一致性。

## 当前项目架构分析

### 项目结构

```
/crates
  /cargo-vcontract      # 主要命令行工具
    /src
      /cmd              # 命令实现
        /utils          # 命令共用工具
        /mod.rs         # 命令模块定义
        /build.rs       # 构建命令
        /call.rs        # 调用命令
        /deploy.rs      # 部署命令
        /...            # 其他命令
      /services         # 服务层实现
        /error_service.rs  # 错误处理服务
        /...            # 其他服务
      /error.rs         # 错误处理工具
      /main.rs          # 入口点
  /build               # 构建库
  /metadata            # 元数据处理库
```

### 当前输出处理方式

目前，项目中的输出处理存在以下问题：

1. **格式分散**：每个命令各自处理JSON和普通文本输出格式
2. **代码重复**：多个命令中存在类似的输出格式化代码
3. **风格不一致**：不同命令的输出风格不统一
4. **紧耦合**：输出逻辑与命令业务逻辑混合

例如，在`DeployCommand`中，输出处理代码直接混合在业务逻辑中：

```rust
if is_json {
    println!("{}", format_output(result, true));
} else {
    println!("\n{}: {}", "Transaction Hash".green().bold(), tx_hash);
    // 更多输出代码...
}
```

## 实施计划

### 1. 创建输出模块结构

**文件路径**：`/crates/cargo-vcontract/src/output/mod.rs`

创建专用的输出模块，包含以下组件：

- `OutputFormatter` - 核心格式化类
- `OutputType` - 输出类型枚举（JSON/Text）
- `OutputTheme` - 输出主题配置
- 各种命令的输出结果类型定义

### 2. 定义输出数据结构

**文件路径**：`/crates/cargo-vcontract/src/output/models.rs`

为每个命令创建标准化的输出数据结构：

- `DeployOutput` - 部署命令输出结构
- `BuildOutput` - 构建命令输出结构
- `CallOutput` - 调用命令输出结构
- 等等...

### 3. 实现 OutputFormatter 类

**文件路径**：`/crates/cargo-vcontract/src/output/formatter.rs`

```rust
pub struct OutputFormatter {
    output_type: OutputType,
    verbose: bool,
}

impl OutputFormatter {
    pub fn new(output_type: OutputType, verbose: bool) -> Self {
        Self { output_type, verbose }
    }

    // 为每个命令输出类型提供专用格式化方法
    pub fn format_deploy(&self, output: DeployOutput) -> String { ... }
    pub fn format_build(&self, output: BuildOutput) -> String { ... }
    // 其他命令的格式化方法...

    // 共用格式化工具方法
    fn format_json<T: Serialize>(&self, data: T) -> String { ... }
    fn highlight_key(&self, key: &str) -> String { ... }
}
```

### 4. 修改各命令实现，使用新的输出格式化器

**涉及文件**：
- `/crates/cargo-vcontract/src/cmd/build.rs`
- `/crates/cargo-vcontract/src/cmd/deploy.rs`
- `/crates/cargo-vcontract/src/cmd/call.rs`
- 其他命令实现文件

修改流程：
1. 从命令实现中提取输出逻辑
2. 将业务结果转换为标准输出数据结构
3. 使用 `OutputFormatter` 格式化结果

### 5. 更新主程序，集中处理输出

**文件路径**：`/crates/cargo-vcontract/src/main.rs`

修改 `exec` 函数，使用统一的输出处理方式：

```rust
fn exec(cmd: Command) -> ServiceResult<()> {
    match &cmd {
        Command::Build(build) => {
            let result = build.exec()?;
            let formatter = OutputFormatter::new(
                build.get_output_type(), 
                build.is_verbose()
            );
            println!("{}", formatter.format_build(result));
            Ok()
        }
        // 其他命令...
    }
}
```

## 具体子任务分解

### 任务 2.1：创建输出模块基础结构

**涉及文件**：
- `/crates/cargo-vcontract/src/output/mod.rs`
- `/crates/cargo-vcontract/src/output/models.rs`
- `/crates/cargo-vcontract/src/output/formatter.rs`

**操作描述**：
1. 创建输出模块目录结构
2. 定义基本接口和类型
3. 实现核心 `OutputFormatter` 类

**预期输出**：
- 完整的输出模块基础结构
- 可供命令实现使用的格式化接口

**技术说明**：
- 使用 Rust traits 定义格式化接口
- 使用泛型实现通用格式化逻辑
- 确保代码可测试性

### 任务 2.2：实现构建命令的输出格式化

**涉及文件**：
- `/crates/cargo-vcontract/src/cmd/build.rs`
- `/crates/cargo-vcontract/src/output/models.rs`（添加 `BuildOutput`）
- `/crates/cargo-vcontract/src/output/formatter.rs`（实现 `format_build`）

**操作描述**：
1. 定义 `BuildOutput` 结构体
2. 实现 `format_build` 方法
3. 修改 `BuildCommand` 使用新的输出格式化器

**预期输出**：
- 改进的构建命令输出
- 标准化的 JSON 和文本输出格式

**技术说明**：
- 保持向后兼容性
- 确保 JSON 格式符合合理的数据结构设计

### 任务 2.3：实现部署命令的输出格式化

**涉及文件**：
- `/crates/cargo-vcontract/src/cmd/deploy.rs`
- `/crates/cargo-vcontract/src/output/models.rs`（添加 `DeployOutput`）
- `/crates/cargo-vcontract/src/output/formatter.rs`（实现 `format_deploy`）

**操作描述**：
1. 定义 `DeployOutput` 结构体
2. 实现 `format_deploy` 方法
3. 修改 `DeployCommand` 使用新的输出格式化器

**预期输出**：
- 改进的部署命令输出
- 标准化的交易结果显示

**技术说明**：
- 确保错误显示与新的输出格式兼容
- 优化长文本的展示方式

### 任务 2.4：实现调用命令的输出格式化

**涉及文件**：
- `/crates/cargo-vcontract/src/cmd/call.rs`
- `/crates/cargo-vcontract/src/output/models.rs`（添加 `CallOutput`）
- `/crates/cargo-vcontract/src/output/formatter.rs`（实现 `format_call`）

**操作描述**：
1. 定义 `CallOutput` 结构体
2. 实现 `format_call` 方法
3. 修改 `CallCommand` 使用新的输出格式化器

**预期输出**：
- 改进的调用命令输出
- 标准化的函数调用结果显示

**技术说明**：
- 确保复杂返回值的良好展示
- 优化大型数据集的展示

### 任务 2.5：实现查询命令的输出格式化

**涉及文件**：
- `/crates/cargo-vcontract/src/cmd/query.rs`
- `/crates/cargo-vcontract/src/output/models.rs`（添加 `QueryOutput`）
- `/crates/cargo-vcontract/src/output/formatter.rs`（实现 `format_query`）

**操作描述**：
1. 定义 `QueryOutput` 结构体
2. 实现 `format_query` 方法
3. 修改 `QueryCommand` 使用新的输出格式化器

**预期输出**：
- 改进的查询命令输出
- 可读性更好的查询结果

**技术说明**：
- 确保输出符合 REST API 最佳实践
- 添加适当的颜色和格式

### 任务 2.6：实现其他命令的输出格式化

**涉及文件**：
- 剩余的命令实现文件
- `/crates/cargo-vcontract/src/output/models.rs`（添加相应输出结构体）
- `/crates/cargo-vcontract/src/output/formatter.rs`（实现相应格式化方法）

**操作描述**：
1. 为每个剩余命令定义输出结构体
2. 实现相应的格式化方法
3. 修改命令实现使用新的输出格式化器

**预期输出**：
- 所有命令使用统一的输出格式化机制

**技术说明**：
- 确保一致的用户体验
- 维护清晰的代码结构

### 任务 2.7：更新主程序和错误处理

**涉及文件**：
- `/crates/cargo-vcontract/src/main.rs`
- `/crates/cargo-vcontract/src/error.rs`

**操作描述**：
1. 修改 `exec` 函数使用统一的输出处理
2. 更新错误处理以与新的输出格式兼容

**预期输出**：
- 主程序使用统一的输出格式化
- 错误处理与输出格式化集成

**技术说明**：
- 确保错误和正常输出的一致性
- 保持清晰的代码结构

### 任务 2.8：添加单元测试

**涉及文件**：
- `/crates/cargo-vcontract/src/output/tests/mod.rs`

**操作描述**：
1. 为输出格式化器添加单元测试
2. 测试各种输出场景

**预期输出**：
- 完整的测试套件
- 确保所有输出格式正确

**技术说明**：
- 使用模拟数据测试输出格式
- 确保边缘情况处理正确

## 实施顺序

建议按照以下顺序实施：

1. 任务 2.1：创建输出模块基础结构
2. 任务 2.7：更新主程序和错误处理（部分实现）
3. 任务 2.2 - 2.6：按优先级实现各命令的输出格式化
   - 先实现使用频率最高的命令（build, deploy, call）
   - 然后实现其他命令
4. 任务 2.7：完成主程序和错误处理更新
5. 任务 2.8：添加单元测试

## 实施注意事项

1. **保持向后兼容性**：确保现有脚本和工作流不会因为输出格式变化而中断
2. **逐步实施**：一次完成一个命令的重构，保持项目可构建
3. **保持一致性**：所有命令应使用相同的输出风格
4. **考虑国际化**：设计时考虑未来可能的多语言支持
5. **性能考虑**：大型输出的处理应该高效

## 预期成果

1. 统一、一致的命令输出风格
2. 减少代码重复
3. 更易于维护的代码结构
4. 更好的用户体验
5. 为未来功能扩展（如本地化、主题支持）提供基础
