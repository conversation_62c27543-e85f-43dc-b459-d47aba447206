use crate::output::{OutputFormatter, models::{BuildOutput, OperationStatus}};

#[test]
fn debug_build_output() {
    let build_output = BuildOutput {
        status: OperationStatus {
            success: true,
            message: Some("Build completed successfully".to_string()),
        },
        contract_file: Some("/path/to/contract.contract".to_string()),
        metadata_file: Some("/path/to/metadata.json".to_string()),
        wasm_file: Some("/path/to/contract.wasm".to_string()),
        contract_size: Some(228355),
        build_time: None,
        optimization_enabled: true,
        optimization_info: Some("Size: 344.7K -> 228.4K".to_string()),
    };

    let formatter = OutputFormatter::from_flags(false, true);
    let output = formatter.format_build(build_output);
    
    println!("Actual output:\n{}", output);
    println!("Contains '228355 bytes': {}", output.contains("228355 bytes"));
}
