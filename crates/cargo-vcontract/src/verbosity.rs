use crate::output::OutputFormat;
use clap::Args;

/// Flags for controlling output verbosity and format
#[derive(Debug, Default, Args)]
pub struct Verbosity {
    /// Use verbose output (ignored if --json is specified)
    #[clap(long)]
    pub verbose: bool,

    /// Output the result in JSON format
    #[clap(long)]
    pub json: bool,
}

impl Verbosity {
    /// Get the output format based on the flags
    pub fn format(&self) -> OutputFormat {
        OutputFormat::from_flags(self.json, self.verbose)
    }

    /// Check if JSON output is requested
    ///
    /// Note: This is mutually exclusive with verbose output.
    /// If JSON output is enabled, verbose flag is ignored.
    pub fn is_json(&self) -> bool {
        self.json
    }

    /// Check if verbose output is requested
    ///
    /// Note: This is mutually exclusive with JSON output.
    /// If JSON output is enabled, this will return false.
    pub fn is_verbose(&self) -> bool {
        !self.json && self.verbose
    }
}
