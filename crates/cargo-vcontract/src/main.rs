mod cmd;
mod error;
mod output;
mod services;

use crate::cmd::{
    config::ConfigCommand, Build<PERSON>ommand, CallCommand, CommandOutput, DeployCommand,
    ForkCommand, KeyCommand, QueryCommand, ReceiptCommand, UpgradeCommand,
};

use crate::output::{
    models::{GenericOutput, NewCommandOutput, OperationStatus, VersionOutput},
    OutputFormatter,
};
use crate::services::ServiceResult;

use clap::{Parser, Subcommand};
use std::path::PathBuf;

/// VContract is a set of utilities to develop Wasm smart contracts.
#[derive(Debug, Parser)]
#[clap(name = "cargo-vcontract", bin_name = "cargo")]
pub struct Cli {
    #[clap(subcommand)]
    command: CargoSubcommand,
}

#[derive(Debug, Subcommand)]
pub enum CargoSubcommand {
    /// VContract utilities for Wasm smart contracts
    #[clap(name = "vcontract")]
    Vcontract(VcontractCli),
}

#[derive(Debug, Parser)]
pub struct VcontractCli {
    #[clap(subcommand)]
    command: Commands,
}

#[derive(Debug, Subcommand)]
pub enum Commands {
    /// Setup and create a new smart contract project
    #[clap(name = "new")]
    New {
        /// Name of the contract project
        name: String,
        /// Optional target directory (default: current directory)
        target_dir: Option<PathBuf>,
    },
    /// Build the smart contract
    #[clap(name = "build")]
    Build(BuildCommand),
    /// Deploy a smart contract
    #[clap(name = "deploy")]
    Deploy(DeployCommand),
    /// Call a smart contract function
    #[clap(name = "call")]
    Call(CallCommand),
    /// Query a smart contract function
    #[clap(name = "query")]
    Query(QueryCommand),
    /// Upgrade a smart contract
    #[clap(name = "upgrade")]
    Upgrade(UpgradeCommand),
    /// Fork a smart contract
    #[clap(name = "fork")]
    Fork(ForkCommand),
    /// Get transaction receipt
    #[clap(name = "receipt")]
    Receipt(ReceiptCommand),
    /// Manage configuration
    #[clap(name = "config")]
    Config(ConfigCommand),
    /// Manage authentication keys
    #[clap(name = "key")]
    Key(KeyCommand),
    /// Show version information
    #[clap(name = "version")]
    Version,
    /// Generate shell completion script
    #[clap(name = "completion")]
    Completion {
        /// The shell type (e.g., bash, zsh)
        #[clap(value_parser)]
        shell: String,
    },
}

fn main() {
    let cli = Cli::parse();

    // Try to determine if JSON output was requested before moving cli
    let is_json = match &cli.command {
        CargoSubcommand::Vcontract(vcontract_cli) => {
            match &vcontract_cli.command {
                Commands::Build(cmd) => cmd.is_json(),
                Commands::Deploy(cmd) => cmd.is_json(),
                Commands::Call(cmd) => cmd.is_json(),
                Commands::Query(cmd) => cmd.is_json(),
                Commands::Upgrade(cmd) => cmd.is_json(),
                Commands::Fork(cmd) => cmd.is_json(),
                Commands::Receipt(cmd) => cmd.is_json(),
                Commands::Config(_) => false, // Config command doesn't support JSON mode
                Commands::Key(_) => false,    // Key command doesn't support JSON mode
                _ => false,
            }
        }
    };

    if let Err(e) = exec(cli) {

        if is_json {
            // Output error in JSON format
            let error_output = crate::output::models::ErrorOutput {
                code: e.error_type().to_string(),
                message: e.to_string(),
                context: Some(serde_json::json!({"details": e.details()})),
            };
            let formatter = crate::output::OutputFormatter::from_flags(true, false);
            if let Ok(formatted) = formatter.format_error(&error_output) {
                eprintln!("{}", formatted);
            } else {
                eprintln!("{{\"error\":\"{}\",\"success\":false}}", e);
            }
        } else {
            eprintln!("Error: {}", e);
        }
        std::process::exit(1);
    }
}

fn exec(cli: Cli) -> ServiceResult<()> {
    match cli.command {
        CargoSubcommand::Vcontract(vcontract_cli) => exec_vcontract(vcontract_cli),
    }
}

fn exec_vcontract(cli: VcontractCli) -> ServiceResult<()> {
    match cli.command {
        Commands::New { name, target_dir } => {
            let formatter = OutputFormatter::from_flags(false, false);
            let output = NewCommandOutput {
                status: OperationStatus {
                    success: true,
                    message: Some(format!("Project '{}' created successfully", name)),
                    code: "SUCCESS".to_string(),
                    context: None,
                },
                project_name: name,
                target_directory: target_dir.map(|p| p.to_string_lossy().to_string()),
                template_used: Some("default".to_string()),
            };
            println!("{}", formatter.format_new(output));
            Ok(())
        }
        Commands::Build(cmd) => match cmd.exec() {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        },
        Commands::Deploy(cmd) => match cmd.exec() {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        },
        Commands::Call(cmd) => match cmd.exec() {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        },
        Commands::Query(cmd) => match cmd.exec() {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        },
        Commands::Upgrade(cmd) => match cmd.exec() {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        },
        Commands::Fork(cmd) => cmd.exec(),
        Commands::Receipt(cmd) => cmd.exec(),
        Commands::Config(cmd) => cmd.exec(),
        Commands::Key(cmd) => cmd.exec(),
        Commands::Version => {
            let formatter = OutputFormatter::human(false);
            let output = VersionOutput {
                status: OperationStatus {
                    success: true,
                    message: Some(format!(
                        "{} v{}",
                        env!("CARGO_PKG_NAME"),
                        env!("CARGO_PKG_VERSION")
                    )),
                    code: "SUCCESS".to_string(),
                    context: None,
                },
                version: env!("CARGO_PKG_VERSION").to_string(),
                package_name: env!("CARGO_PKG_NAME").to_string(),
                description:
                    "Utilities to develop Wasm smart contracts for VGraph network"
                        .to_string(),
                build_info: Some(format!(
                    "Built with rustc {}",
                    env!("CARGO_PKG_RUST_VERSION")
                )),
            };
            println!("{}", formatter.format_version(output));
            Ok(())
        }
        Commands::Completion { shell } => {
            let formatter = OutputFormatter::from_flags(false, false);
            let output = GenericOutput {
                status: OperationStatus {
                    success: true,
                    message: Some("Generated shell completion script".to_string()),
                    code: "SUCCESS".to_string(),
                    context: None,
                },
                data: Some(serde_json::json!({
                    "shell": shell,
                })),
            };
            println!("{}", formatter.format_generic(&output).unwrap());
            Ok(())
        }
    }
}
