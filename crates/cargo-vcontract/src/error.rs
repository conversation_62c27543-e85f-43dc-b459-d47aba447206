use crate::cmd::config::ConfigCommand;
use crate::cmd::CommandOutput;
use crate::cmd::{
    B<PERSON>Command, CallCommand, DeployCommand, ForkCommand, KeyCommand, QueryCommand,
    ReceiptCommand, UpgradeCommand,
};
use crate::output::{
    models::{GenericOutput, OperationStatus},
    OutputFormatter,
};
use crate::services::ServiceError;
use serde_json::json;

/// Central error handling function that formats output based on JSON flag
pub fn handle_error(error: &ServiceError, is_json: bool) {
    let formatter = OutputFormatter::from_flags(is_json, false);
    let output = GenericOutput {
        status: OperationStatus {
            success: false,
            message: Some(error.to_string()),
            code: "ERROR".to_string(),
            context: None,
        },
        data: Some(serde_json::json!({
            "error_type": error.error_type(),
            "details": error.details(),
        })),
    };

    if is_json {
        println!("{}", formatter.format_generic(&output).unwrap());
    } else {
        eprintln!("{}", formatter.format_generic(&output).unwrap());
    }
}

/// Check if a command requires JSON output
pub fn check_is_json(cmd: &crate::Commands) -> bool {
    match cmd {
        crate::Commands::Build(c) => c.is_json(),
        crate::Commands::Deploy(c) => c.is_json(),
        crate::Commands::Call(c) => c.is_json(),
        crate::Commands::Query(c) => c.is_json(),
        crate::Commands::Upgrade(c) => c.is_json(),
        crate::Commands::Fork(c) => c.is_json(),
        crate::Commands::Receipt(c) => c.is_json(),
        crate::Commands::Config(c) => c.is_json(),
        crate::Commands::Key(c) => c.is_json(),
        // For commands without explicit JSON flags, default to false
        _ => false,
    }
}
