use thiserror::Error;

/// Unified error type for all service layer operations
#[derive(Error, Debug)]
#[allow(dead_code)] // Some variants reserved for future use
pub enum ServiceError {
    #[error("Network error: {message}")]
    Network { message: String },

    #[error("Validation error: {message}")]
    Validation { message: String },

    #[error("Configuration error: {message}")]
    Config { message: String },

    #[error("Authentication error: {message}")]
    Auth { message: String },

    #[error("Metadata error: {message}")]
    Metadata { message: String },

    #[error("Contract error: {message}")]
    Contract { message: String },

    #[error("File system error: {message}")]
    FileSystem { message: String },

    #[error("Internal error: {message}")]
    Internal { message: String },
}

/// Convenience type alias for service layer results
pub type ServiceResult<T> = Result<T, ServiceError>;

impl ServiceError {
    /// Create a network error
    pub fn network(message: impl Into<String>) -> Self {
        ServiceError::Network {
            message: message.into(),
        }
    }

    /// Create a validation error
    pub fn validation(message: impl Into<String>) -> Self {
        ServiceError::Validation {
            message: message.into(),
        }
    }

    /// Create an authentication error
    pub fn auth(message: impl Into<String>) -> Self {
        ServiceError::Auth {
            message: message.into(),
        }
    }

    /// Create a file system error
    pub fn file_system(message: impl Into<String>) -> Self {
        ServiceError::FileSystem {
            message: message.into(),
        }
    }

    /// Create a contract error
    pub fn contract(message: impl Into<String>) -> Self {
        ServiceError::Contract {
            message: message.into(),
        }
    }

    /// Create an internal error
    pub fn internal(message: impl Into<String>) -> Self {
        ServiceError::Internal {
            message: message.into(),
        }
    }

    pub fn error_type(&self) -> &'static str {
        match self {
            ServiceError::Network { .. } => "NetworkError",
            ServiceError::Validation { .. } => "ValidationError",
            ServiceError::Config { .. } => "ConfigError",
            ServiceError::Auth { .. } => "AuthError",
            ServiceError::Metadata { .. } => "MetadataError",
            ServiceError::Contract { .. } => "ContractError",
            ServiceError::FileSystem { .. } => "FileSystemError",
            ServiceError::Internal { .. } => "InternalError",
        }
    }

    pub fn details(&self) -> String {
        match self {
            ServiceError::Network { message } => message.clone(),
            ServiceError::Validation { message } => message.clone(),
            ServiceError::Config { message } => message.clone(),
            ServiceError::Auth { message } => message.clone(),
            ServiceError::Metadata { message } => message.clone(),
            ServiceError::Contract { message } => message.clone(),
            ServiceError::FileSystem { message } => message.clone(),
            ServiceError::Internal { message } => message.clone(),
        }
    }
}

// Convert from anyhow::Error to ServiceError
impl From<anyhow::Error> for ServiceError {
    fn from(err: anyhow::Error) -> Self {
        ServiceError::internal(err.to_string())
    }
}

// Convert from vgraph_sdk_core errors if they exist
// Note: This would need to be adjusted based on actual SDK error types
impl From<Box<dyn std::error::Error + Send + Sync>> for ServiceError {
    fn from(err: Box<dyn std::error::Error + Send + Sync>) -> Self {
        ServiceError::internal(err.to_string())
    }
}
