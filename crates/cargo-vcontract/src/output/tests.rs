#[cfg(test)]
mod tests {
    use super::*;
    use crate::output::{
        builder::OutputBuilder,
        formatter::{JsonFormatter, OutputFormat, TextFormatter},
        models::*,
    };
    use crate::verbosity::Verbosity;
    use serde_json::json;

    #[test]
    fn test_deploy_output_text_format() {
        let deploy_output = DeployOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract deployment successful".to_string()),
            },
            transaction_hash: "0x1234567890abcdef".to_string(),
            contract_address: "0xabcdef1234567890".to_string(),
            code_hash: "0xfedcba0987654321".to_string(),
            contract_name: "TestToken".to_string(),
            fuel_consumed: Some(250000),
            receipt: Some(json!({
                "status": "success",
                "gas_used": 250000
            })),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_deploy(deploy_output);

        assert!(output.contains("Contract deployment successful!"));
        assert!(output.contains("Transaction Hash"));
        assert!(output.contains("0x1234567890abcdef"));
        assert!(output.contains("Contract Address"));
        assert!(output.contains("0xabcdef1234567890"));
        assert!(output.contains("TestToken"));
        assert!(output.contains("250000"));
    }

    #[test]
    fn test_deploy_output_json_format() {
        let deploy_output = DeployOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract deployment successful".to_string()),
            },
            transaction_hash: "0x1234567890abcdef".to_string(),
            contract_address: "0xabcdef1234567890".to_string(),
            code_hash: "0xfedcba0987654321".to_string(),
            contract_name: "TestToken".to_string(),
            fuel_consumed: Some(250000),
            receipt: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_deploy(deploy_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["transaction_hash"], "0x1234567890abcdef");
        assert_eq!(parsed["contract_address"], "0xabcdef1234567890");
        assert_eq!(parsed["contract_name"], "TestToken");
        assert_eq!(parsed["fuel_consumed"], 250000);
    }

    #[test]
    fn test_build_output_text_format() {
        let build_output = BuildOutput {
            status: OperationStatus {
                success: true,
                message: Some("Build completed successfully".to_string()),
            },
            contract_file: Some("/path/to/contract.contract".to_string()),
            metadata_file: Some("/path/to/metadata.json".to_string()),
            wasm_file: Some("/path/to/contract.wasm".to_string()),
            contract_size: Some(228355),
            build_time: None,
            optimization_enabled: true,
            optimization_info: Some("Size: 344.7K -> 228.4K".to_string()),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_build(build_output);

        assert!(output.contains("Build successful!"));
        assert!(output.contains("Contract File"));
        assert!(output.contains("228355"));
        assert!(output.contains("Optimization"));
        assert!(output.contains("Enabled"));
        assert!(output.contains("Size: 344.7K -> 228.4K"));
    }

    #[test]
    fn test_call_output_text_format() {
        let call_output = CallOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract call successful".to_string()),
            },
            transaction_hash: "0x1234567890abcdef".to_string(),
            function_name: "transfer".to_string(),
            result: Some(json!({
                "balance": 1000,
                "success": true
            })),
            fuel_consumed: Some(150000),
            receipt: Some(json!({
                "status": "success",
                "gas_used": 150000
            })),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_call(call_output);

        assert!(output.contains("Function call successful!"));
        assert!(output.contains("Transaction Hash"));
        assert!(output.contains("0x1234567890abcdef"));
        assert!(output.contains("Function Name"));
        assert!(output.contains("transfer"));
        assert!(output.contains("150000"));
    }

    #[test]
    fn test_call_output_json_format() {
        let call_output = CallOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract call successful".to_string()),
            },
            transaction_hash: "0x1234567890abcdef".to_string(),
            function_name: "transfer".to_string(),
            result: Some(json!({
                "balance": 1000,
                "success": true
            })),
            fuel_consumed: Some(150000),
            receipt: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_call(call_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["transaction_hash"], "0x1234567890abcdef");
        assert_eq!(parsed["function_name"], "transfer");
        assert_eq!(parsed["fuel_consumed"], 150000);
        assert_eq!(parsed["result"]["balance"], 1000);
    }

    #[test]
    fn test_query_output_text_format() {
        let query_output = QueryOutput {
            status: OperationStatus {
                success: true,
                message: Some("Query executed successfully".to_string()),
            },
            function_name: "get_balance".to_string(),
            result: Some(json!({
                "balance": 500,
                "owner": "0x123456789"
            })),
            execution_time: Some("1.2ms".to_string()),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_query(query_output);

        assert!(output.contains("Query successful!"));
        assert!(output.contains("Function Name"));
        assert!(output.contains("get_balance"));
        assert!(output.contains("balance"));
        assert!(output.contains("1.2ms"));
    }

    #[test]
    fn test_query_output_json_format() {
        let query_output = QueryOutput {
            status: OperationStatus {
                success: true,
                message: Some("Query executed successfully".to_string()),
            },
            function_name: "get_balance".to_string(),
            result: Some(json!({
                "balance": 500,
                "owner": "0x123456789"
            })),
            execution_time: Some("1.2ms".to_string()),
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_query(query_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["function_name"], "get_balance");
        assert_eq!(parsed["result"]["balance"], 500);
        assert_eq!(parsed["execution_time"], "1.2ms");
    }

    #[test]
    fn test_upgrade_output_text_format() {
        let upgrade_output = UpgradeOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract upgrade successful".to_string()),
            },
            transaction_hash: "0x123abc456def".to_string(),
            contract_address: "0x456def789ghi".to_string(),
            old_code_hash: "0x789ghi012jkl".to_string(),
            new_code_hash: "0xabcdef123456".to_string(),
            fuel_consumed: Some(75000),
            receipt: Some(json!({
                "status": "success",
                "gas_used": 75000
            })),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_upgrade(upgrade_output);

        assert!(output.contains("Contract upgrade successful!"));
        assert!(output.contains("Transaction Hash"));
        assert!(output.contains("0x123abc456def"));
        assert!(output.contains("Contract Address"));
        assert!(output.contains("0x456def789ghi"));
        assert!(output.contains("75000"));
    }

    #[test]
    fn test_upgrade_output_json_format() {
        let upgrade_output = UpgradeOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract upgrade successful".to_string()),
            },
            transaction_hash: "0x123abc456def".to_string(),
            contract_address: "0x456def789ghi".to_string(),
            old_code_hash: "0x789ghi012jkl".to_string(),
            new_code_hash: "0xabcdef123456".to_string(),
            fuel_consumed: Some(75000),
            receipt: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_upgrade(upgrade_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["transaction_hash"], "0x123abc456def");
        assert_eq!(parsed["contract_address"], "0x456def789ghi");
        assert_eq!(parsed["fuel_consumed"], 75000);
    }

    #[test]
    fn test_fork_output_text_format() {
        let fork_output = ForkOutput {
            status: OperationStatus {
                success: true,
                message: Some("Fork contract successful".to_string()),
            },
            original_transaction_hash: "0xabc123def456".to_string(),
            new_transaction_hash: "0xdef456abc123".to_string(),
            new_contract_address: Some("0x789ghi456jkl".to_string()),
            fuel_consumed: Some(85000),
            receipt: Some(json!({
                "status": "success",
                "gas_used": 85000
            })),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_fork(fork_output);

        assert!(output.contains("Transaction fork successful!"));
        assert!(output.contains("New Transaction Hash"));
        assert!(output.contains("0xdef456abc123"));
        assert!(output.contains("New Contract Address"));
        assert!(output.contains("0x789ghi456jkl"));
        assert!(output.contains("85000"));
    }

    #[test]
    fn test_fork_output_json_format() {
        let fork_output = ForkOutput {
            status: OperationStatus {
                success: true,
                message: Some("Fork contract successful".to_string()),
            },
            original_transaction_hash: "0xabc123def456".to_string(),
            new_transaction_hash: "0xdef456abc123".to_string(),
            new_contract_address: Some("0x789ghi456jkl".to_string()),
            fuel_consumed: Some(85000),
            receipt: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_fork(fork_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["original_transaction_hash"], "0xabc123def456");
        assert_eq!(parsed["new_transaction_hash"], "0xdef456abc123");
        assert_eq!(parsed["new_contract_address"], "0x789ghi456jkl");
        assert_eq!(parsed["fuel_consumed"], 85000);
    }

    #[test]
    fn test_receipt_output_text_format() {
        let receipt_output = ReceiptOutput {
            status: OperationStatus {
                success: true,
                message: Some("Receipt retrieved successfully".to_string()),
            },
            transaction_hash: "0xabc123def456".to_string(),
            receipt: json!({
                "status": "success",
                "gas_used": 50000,
                "block_hash": "0xblock123"
            }),
            block_number: Some(12345),
            timestamp: Some("2024-01-25T10:30:00Z".to_string()),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_receipt(receipt_output);

        assert!(output.contains("Receipt retrieved successfully!"));
        assert!(output.contains("Transaction Hash"));
        assert!(output.contains("0xabc123def456"));
        assert!(output.contains("Block Number"));
        assert!(output.contains("12345"));
        assert!(output.contains("2024-01-25T10:30:00Z"));
    }

    #[test]
    fn test_receipt_output_json_format() {
        let receipt_output = ReceiptOutput {
            status: OperationStatus {
                success: true,
                message: Some("Receipt retrieved successfully".to_string()),
            },
            transaction_hash: "0xabc123def456".to_string(),
            receipt: json!({
                "status": "success",
                "gas_used": 50000,
                "block_hash": "0xblock123"
            }),
            block_number: Some(12345),
            timestamp: Some("2024-01-25T10:30:00Z".to_string()),
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_receipt(receipt_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["transaction_hash"], "0xabc123def456");
        assert_eq!(parsed["block_number"], 12345);
        assert_eq!(parsed["timestamp"], "2024-01-25T10:30:00Z");
    }

    #[test]
    fn test_config_output_text_format() {
        let config_output = ConfigOutput {
            status: OperationStatus {
                success: true,
                message: Some("Configuration updated".to_string()),
            },
            action: "set".to_string(),
            config_key: Some("default_node".to_string()),
            config_value: Some("127.0.0.1:9877".to_string()),
            all_config: None,
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_config(config_output);

        assert!(output.contains("Configuration updated!"));
        assert!(output.contains("default_node"));
        assert!(output.contains("127.0.0.1:9877"));
    }

    #[test]
    fn test_config_output_json_format() {
        let config_output = ConfigOutput {
            status: OperationStatus {
                success: true,
                message: Some("Configuration updated".to_string()),
            },
            action: "set".to_string(),
            config_key: Some("default_node".to_string()),
            config_value: Some("127.0.0.1:9877".to_string()),
            all_config: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_config(config_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["action"], "set");
        assert_eq!(parsed["config_key"], "default_node");
        assert_eq!(parsed["config_value"], "127.0.0.1:9877");
    }

    #[test]
    fn test_key_output_add_text_format() {
        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some("Key 'main' added successfully".to_string()),
            },
            action: "add".to_string(),
            key_info: Some(json!({
                "name": "main",
                "address": "0x1234567890abcdef",
                "is_active": true,
                "created_at": "1642684800"
            })),
            message: None,
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_key(key_output);

        assert!(output.contains("Key 'main' added successfully"));
        assert!(output.contains("main"));
        assert!(output.contains("0x1234567890abcdef"));
    }

    #[test]
    fn test_key_output_add_json_format() {
        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some("Key 'main' added successfully".to_string()),
            },
            action: "add".to_string(),
            key_info: Some(json!({
                "name": "main",
                "address": "0x1234567890abcdef",
                "is_active": true,
                "created_at": "1642684800"
            })),
            message: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_key(key_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["action"], "add");
        assert_eq!(parsed["key_info"]["name"], "main");
        assert_eq!(parsed["key_info"]["address"], "0x1234567890abcdef");
        assert_eq!(parsed["key_info"]["is_active"], true);
    }

    #[test]
    fn test_key_output_list_text_format() {
        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some("Found 2 key(s)".to_string()),
            },
            action: "list".to_string(),
            key_info: Some(json!({
                "keys": {
                    "main": {
                        "address": "0x1234567890abcdef",
                        "created_at": "1642684800",
                        "is_active": true
                    },
                    "test": {
                        "address": "0xfedcba0987654321",
                        "created_at": "1642684900",
                        "is_active": false
                    }
                },
                "active_key": "main",
                "total_keys": 2
            })),
            message: None,
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_key(key_output);

        assert!(output.contains("Found 2 key(s)"));
        assert!(output.contains("main"));
        assert!(output.contains("test"));
    }

    #[test]
    fn test_key_output_list_json_format() {
        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some("Found 2 key(s)".to_string()),
            },
            action: "list".to_string(),
            key_info: Some(json!({
                "keys": {
                    "main": {
                        "address": "0x1234567890abcdef",
                        "created_at": "1642684800",
                        "is_active": true
                    },
                    "test": {
                        "address": "0xfedcba0987654321",
                        "created_at": "1642684900",
                        "is_active": false
                    }
                },
                "active_key": "main",
                "total_keys": 2
            })),
            message: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_key(key_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["action"], "list");
        assert_eq!(parsed["key_info"]["total_keys"], 2);
        assert_eq!(parsed["key_info"]["active_key"], "main");
    }

    #[test]
    fn test_key_output_generate_text_format() {
        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some("Key 'test' generated and set as active".to_string()),
            },
            action: "generate".to_string(),
            key_info: Some(json!({
                "name": "test",
                "private_key": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "public_key": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "address": "0x1234567890abcdef",
                "seed_used": false,
                "auto_activate": true,
                "added_to_store": true,
                "activated": true
            })),
            message: None,
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_key(key_output);

        assert!(output.contains("Key 'test' generated and set as active"));
        assert!(output.contains("test"));
        assert!(output.contains("0x1234567890abcdef"));
    }

    #[test]
    fn test_key_output_generate_json_format() {
        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some("Key 'test' generated and set as active".to_string()),
            },
            action: "generate".to_string(),
            key_info: Some(json!({
                "name": "test",
                "private_key": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "public_key": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "address": "0x1234567890abcdef",
                "seed_used": false,
                "auto_activate": true,
                "added_to_store": true,
                "activated": true
            })),
            message: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_key(key_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["action"], "generate");
        assert_eq!(parsed["key_info"]["name"], "test");
        assert_eq!(parsed["key_info"]["added_to_store"], true);
        assert_eq!(parsed["key_info"]["activated"], true);
    }

    #[test]
    fn test_generic_output_text_format() {
        let generic_output = GenericOutput {
            status: OperationStatus {
                success: true,
                message: Some("Operation completed successfully".to_string()),
                code: "OPERATION_SUCCESS".to_string(),
                context: None,
            },
            data: Some(json!({
                "key": "value",
                "number": 42
            })),
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_generic(&generic_output);

        assert!(output.contains("Operation completed successfully"));
        assert!(output.contains("key"));
        assert!(output.contains("value"));
        assert!(output.contains("42"));
    }

    #[test]
    fn test_generic_output_json_format() {
        let generic_output = GenericOutput {
            status: OperationStatus {
                success: true,
                message: Some("Operation completed successfully".to_string()),
                code: "OPERATION_SUCCESS".to_string(),
                context: None,
            },
            data: Some(json!({
                "key": "value",
                "number": 42
            })),
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_generic(&generic_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(
            parsed["status"]["message"],
            "Operation completed successfully"
        );
        assert_eq!(parsed["data"]["key"], "value");
        assert_eq!(parsed["data"]["number"], 42);
    }

    #[test]
    fn test_generic_output_error_text_format() {
        let generic_output = GenericOutput {
            status: OperationStatus {
                success: false,
                message: Some("Operation failed".to_string()),
                code: "OPERATION_FAILED".to_string(),
                context: Some(json!({
                    "error_type": "ValidationError",
                    "details": "Invalid input"
                })),
            },
            data: None,
        };

        let formatter = OutputFormatter::from_flags(false, true);
        let output = formatter.format_generic(&generic_output);

        assert!(output.contains("Operation failed"));
        assert!(output.contains("OPERATION_FAILED"));
        assert!(output.contains("ValidationError"));
        assert!(output.contains("Invalid input"));
    }

    #[test]
    fn test_generic_output_error_json_format() {
        let generic_output = GenericOutput {
            status: OperationStatus {
                success: false,
                message: Some("Operation failed".to_string()),
                code: "OPERATION_FAILED".to_string(),
                context: Some(json!({
                    "error_type": "ValidationError",
                    "details": "Invalid input"
                })),
            },
            data: None,
        };

        let formatter = OutputFormatter::from_flags(true, false);
        let output = formatter.format_generic(&generic_output);

        // Parse as JSON to verify it's valid
        let parsed: serde_json::Value = serde_json::from_str(&output).unwrap();
        assert_eq!(parsed["status"]["success"], false);
        assert_eq!(parsed["status"]["message"], "Operation failed");
        assert_eq!(parsed["status"]["code"], "OPERATION_FAILED");
        assert_eq!(parsed["status"]["context"]["error_type"], "ValidationError");
        assert_eq!(parsed["status"]["context"]["details"], "Invalid input");
    }

    #[test]
    fn test_output_builder() {
        let build_output = OutputBuilder::new()
            .with_data(BuildOutput {
                contract_file: Some("/path/to/contract.contract".to_string()),
                metadata_file: Some("/path/to/metadata.json".to_string()),
                wasm_file: Some("/path/to/contract.wasm".to_string()),
                contract_size: Some(228355),
                build_time: None,
                optimization_enabled: true,
                optimization_info: Some("Size: 344.7K -> 228.4K".to_string()),
            })
            .build();

        assert!(build_output.status.success);
        assert_eq!(build_output.status.code, "SUCCESS");
        assert!(build_output.data.is_some());
        assert!(build_output.metadata.is_none());
    }

    #[test]
    fn test_output_builder_error() {
        let build_output = OutputBuilder::<BuildOutput>::error(
            "BUILD_FAILED",
            "Failed to compile contract",
        )
        .build();

        assert!(!build_output.status.success);
        assert_eq!(build_output.status.code, "BUILD_FAILED");
        assert_eq!(
            build_output.status.message,
            Some("Failed to compile contract".to_string())
        );
        assert!(build_output.data.is_none());
    }

    #[test]
    fn test_json_formatter() {
        let formatter = JsonFormatter;
        let value = json!({
            "name": "test",
            "value": 42,
            "nested": {
                "array": [1, 2, 3],
                "object": {"key": "value"}
            }
        });

        let result = formatter.format_value(&value).unwrap();
        assert!(result.contains("\"name\": \"test\""));
        assert!(result.contains("\"value\": 42"));
        assert!(result.contains("\"array\": ["));
    }

    #[test]
    fn test_text_formatter_normal() {
        let formatter = TextFormatter::new(false);
        let value = json!({
            "name": "test",
            "value": 42,
            "nested": {
                "array": [1, 2, 3],
                "object": {"key": "value"}
            }
        });

        let result = formatter.format_value(&value).unwrap();
        assert!(result.contains("name:"));
        assert!(result.contains("value: 42"));
        assert!(result.contains("[...]")); // Compact nested objects
    }

    #[test]
    fn test_text_formatter_verbose() {
        let formatter = TextFormatter::new(true);
        let value = json!({
            "name": "test",
            "value": 42,
            "nested": {
                "array": [1, 2, 3],
                "object": {"key": "value"}
            }
        });

        let result = formatter.format_value(&value).unwrap();
        assert!(result.contains("name: test"));
        assert!(result.contains("value: 42"));
        assert!(result.contains("[0] 1")); // Full array details
        assert!(result.contains("key: value")); // Full nested object details
    }

    #[test]
    fn test_error_formatting() {
        let error = ErrorOutput {
            code: "E001".to_string(),
            message: "Test error".to_string(),
            context: Some(json!({
                "details": "Additional info"
            })),
        };

        // Test JSON format
        let json_formatter = JsonFormatter;
        let json_result = json_formatter.format_error(&error).unwrap();
        assert!(json_result.contains("\"code\": \"E001\""));
        assert!(json_result.contains("\"message\": \"Test error\""));

        // Test text format
        let text_formatter = TextFormatter::new(false);
        let text_result = text_formatter.format_error(&error).unwrap();
        assert!(text_result.contains("Code: E001"));
        assert!(text_result.contains("Message: Test error"));
        assert!(text_result.contains("Context:"));
    }

    #[test]
    fn test_generic_output() {
        let output = GenericOutput {
            status: OperationStatus {
                success: true,
                message: Some("Operation completed".to_string()),
                code: "S001".to_string(),
                context: Some(json!({
                    "timestamp": "2024-01-01"
                })),
            },
            data: Some(json!({
                "result": "success"
            })),
        };

        // Test JSON format
        let json_formatter = JsonFormatter;
        let json_result = json_formatter.format_generic(&output).unwrap();
        assert!(json_result.contains("\"success\": true"));
        assert!(json_result.contains("\"message\": \"Operation completed\""));

        // Test text format
        let text_formatter = TextFormatter::new(true);
        let text_result = text_formatter.format_generic(&output).unwrap();
        assert!(text_result.contains("Success: true"));
        assert!(text_result.contains("Message: Operation completed"));
        assert!(text_result.contains("timestamp: 2024-01-01"));
    }

    #[test]
    fn test_formatter_error_handling() {
        let formatter = OutputFormatter::new(OutputFormat::Json);

        // Test invalid JSON serialization
        let result = formatter.format(&std::f64::NAN);
        assert!(result.is_err());

        if let Err(FormatterError::SerializationError(e)) = result {
            assert!(e.to_string().contains("NaN"));
        } else {
            panic!("Expected SerializationError");
        }
    }

    #[test]
    fn test_empty_collections() {
        let formatter = TextFormatter::new(true);

        // Test empty array
        let empty_array = json!([]);
        let array_result = formatter.format_value(&empty_array).unwrap();
        assert_eq!(array_result.trim(), "[]");

        // Test empty object
        let empty_object = json!({});
        let object_result = formatter.format_value(&empty_object).unwrap();
        assert_eq!(object_result.trim(), "{}");
    }

    #[test]
    fn test_output_format_from_flags() {
        // JSON format takes precedence over verbose
        assert_eq!(OutputFormat::from_flags(true, true), OutputFormat::Json);
        assert_eq!(OutputFormat::from_flags(true, false), OutputFormat::Json);

        // When json is false, verbose determines the text format
        assert_eq!(
            OutputFormat::from_flags(false, true),
            OutputFormat::TextVerbose
        );
        assert_eq!(OutputFormat::from_flags(false, false), OutputFormat::Text);
    }

    #[test]
    fn test_verbosity_format() {
        let v1 = Verbosity {
            json: true,
            verbose: true,
        };
        assert_eq!(v1.format(), OutputFormat::Json);
        assert!(v1.is_json());
        assert!(!v1.is_verbose()); // verbose is ignored when json is true

        let v2 = Verbosity {
            json: true,
            verbose: false,
        };
        assert_eq!(v2.format(), OutputFormat::Json);
        assert!(v2.is_json());
        assert!(!v2.is_verbose());

        let v3 = Verbosity {
            json: false,
            verbose: true,
        };
        assert_eq!(v3.format(), OutputFormat::TextVerbose);
        assert!(!v3.is_json());
        assert!(v3.is_verbose());

        let v4 = Verbosity {
            json: false,
            verbose: false,
        };
        assert_eq!(v4.format(), OutputFormat::Text);
        assert!(!v4.is_json());
        assert!(!v4.is_verbose());
    }

    #[test]
    fn test_json_output_ignores_verbose() {
        let output = GenericOutput {
            status: OperationStatus {
                success: true,
                message: Some("Test message".to_string()),
                code: "TEST_SUCCESS".to_string(),
                context: None,
            },
            data: Some(json!({
                "key": "value",
                "number": 42
            })),
        };

        // Both formatters should produce identical JSON output
        let formatter1 = OutputFormatter::from_flags(true, false);
        let formatter2 = OutputFormatter::from_flags(true, true);

        let output1 = formatter1.format_generic(&output);
        let output2 = formatter2.format_generic(&output);

        assert_eq!(output1, output2);

        // Verify it's valid JSON
        let parsed: serde_json::Value = serde_json::from_str(&output1).unwrap();
        assert_eq!(parsed["status"]["success"], true);
        assert_eq!(parsed["status"]["message"], "Test message");
        assert_eq!(parsed["data"]["key"], "value");
        assert_eq!(parsed["data"]["number"], 42);
    }

    #[test]
    fn test_text_output_respects_verbose() {
        let output = GenericOutput {
            status: OperationStatus {
                success: true,
                message: Some("Test message".to_string()),
                code: "TEST_SUCCESS".to_string(),
                context: None,
            },
            data: Some(json!({
                "key": "value",
                "number": 42
            })),
        };

        let normal_formatter = OutputFormatter::from_flags(false, false);
        let verbose_formatter = OutputFormatter::from_flags(false, true);

        let normal_output = normal_formatter.format_generic(&output);
        let verbose_output = verbose_formatter.format_generic(&output);

        // Both should contain the basic information
        assert!(normal_output.contains("Test message"));
        assert!(verbose_output.contains("Test message"));

        // Verbose output should be longer and contain more details
        assert!(verbose_output.len() > normal_output.len());
    }
}
