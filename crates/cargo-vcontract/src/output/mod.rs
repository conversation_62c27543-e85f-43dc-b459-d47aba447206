//! Output formatting module for cargo-vcontract
//!
//! This module provides unified output formatting for all commands,
//! reducing code duplication and ensuring consistent output styles.

pub mod formatter;
pub mod labels;
pub mod models;

#[cfg(test)]
mod tests;

pub use formatter::OutputFormatter;
pub use models::*;

/// Output format enumeration for formatting decisions
#[derive(Debug, Clone, PartialEq)]
pub enum OutputFormat {
    /// JSON formatted output (always complete, verbosity has no effect)
    Json,
    /// Human-readable text output with normal detail level
    Text,
    /// Human-readable text output with verbose detail level
    TextVerbose,
    /// Human-friendly output without JSON-like syntax
    Human,
    /// Human-friendly output with verbose detail level
    HumanVerbose,
}

impl OutputFormat {
    /// Create from verbosity flags
    pub fn from_flags(is_json: bool, verbose: bool) -> Self {
        if is_json {
            Self::Json
        } else if verbose {
            Self::HumanVerbose
        } else {
            Self::Human
        }
    }
}

/// Helper function to create a new output formatter
pub fn create_formatter(format: OutputFormat) -> OutputFormatter {
    OutputFormatter::new(format)
}
