//! Human-readable field labels for output formatting
//!
//! This module provides mappings from technical field names to user-friendly labels
//! for improved readability in text output.

use std::collections::HashMap;

/// Mapping from field names to human-readable labels
pub struct FieldLabels {
    labels: HashMap<String, String>,
}

impl FieldLabels {
    pub fn new() -> Self {
        let mut labels = HashMap::new();
        
        // Common fields
        labels.insert("status".to_string(), "Status".to_string());
        labels.insert("message".to_string(), "Message".to_string());
        labels.insert("code".to_string(), "Code".to_string());
        labels.insert("success".to_string(), "Success".to_string());
        labels.insert("context".to_string(), "Context".to_string());
        
        // Version command fields
        labels.insert("version".to_string(), "Version".to_string());
        labels.insert("package_name".to_string(), "Package".to_string());
        labels.insert("description".to_string(), "Description".to_string());
        labels.insert("build_info".to_string(), "Build Info".to_string());
        
        // Query/Call command fields
        labels.insert("function_name".to_string(), "Function Name".to_string());
        labels.insert("result".to_string(), "Result".to_string());
        labels.insert("transaction_hash".to_string(), "Transaction Hash".to_string());
        labels.insert("fuel_consumed".to_string(), "Fuel Consumed".to_string());
        labels.insert("receipt".to_string(), "Receipt".to_string());
        
        // Deploy command fields
        labels.insert("contract_address".to_string(), "Contract Address".to_string());
        labels.insert("code_hash".to_string(), "Code Hash".to_string());
        labels.insert("contract_name".to_string(), "Contract Name".to_string());
        
        // Config command fields
        labels.insert("action".to_string(), "Action".to_string());
        labels.insert("config_key".to_string(), "Config Key".to_string());
        labels.insert("config_value".to_string(), "Config Value".to_string());
        labels.insert("all_config".to_string(), "Configuration".to_string());
        labels.insert("config_file".to_string(), "Config File".to_string());
        labels.insert("default_node".to_string(), "Default Node".to_string());
        labels.insert("authentication".to_string(), "Authentication".to_string());
        
        // Key command fields
        labels.insert("key_info".to_string(), "Key Information".to_string());
        labels.insert("active_key".to_string(), "Active Key".to_string());
        labels.insert("keys".to_string(), "Keys".to_string());
        labels.insert("total_keys".to_string(), "Total Keys".to_string());
        labels.insert("address".to_string(), "Address".to_string());
        labels.insert("created_at".to_string(), "Created At".to_string());
        labels.insert("is_active".to_string(), "Is Active".to_string());
        
        // Receipt command fields
        labels.insert("block_number".to_string(), "Block Number".to_string());
        labels.insert("timestamp".to_string(), "Timestamp".to_string());
        labels.insert("block_hash".to_string(), "Block Hash".to_string());
        labels.insert("logs".to_string(), "Logs".to_string());
        labels.insert("contract_address".to_string(), "Contract Address".to_string());
        labels.insert("data".to_string(), "Data".to_string());
        labels.insert("topics".to_string(), "Topics".to_string());
        labels.insert("log_index".to_string(), "Log Index".to_string());
        labels.insert("transaction_index".to_string(), "Transaction Index".to_string());
        
        Self { labels }
    }
    
    /// Get human-readable label for a field name
    pub fn get_label(&self, field_name: &str) -> String {
        self.labels.get(field_name)
            .cloned()
            .unwrap_or_else(|| {
                // Convert snake_case to Title Case as fallback
                field_name
                    .split('_')
                    .map(|word| {
                        let mut chars = word.chars();
                        match chars.next() {
                            None => String::new(),
                            Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
                        }
                    })
                    .collect::<Vec<_>>()
                    .join(" ")
            })
    }
    
    /// Check if a field should be treated as a section header
    pub fn is_section_header(&self, field_name: &str) -> bool {
        matches!(field_name, 
            "status" | "all_config" | "key_info" | "receipt" | "configuration" | "keys"
        )
    }
    
    /// Check if a field should be hidden in human-readable output
    pub fn should_hide_field(&self, field_name: &str) -> bool {
        matches!(field_name, "action" | "context")
    }

    /// Check if a field should be formatted as a timestamp
    pub fn is_timestamp_field(&self, field_name: &str) -> bool {
        matches!(field_name, "created_at" | "timestamp" | "created" | "updated_at" | "modified_at")
    }
}

impl Default for FieldLabels {
    fn default() -> Self {
        Self::new()
    }
}
