//! Output formatter implementation for cargo-vcontract
//!
//! This module provides the core OutputFormatter that handles formatting
//! of command outputs in both JSON and human-readable text formats.

use super::OutputFormat;
use crate::output::models::*;
use crate::output::labels::FieldLabels;
use colored::Colorize;
use serde::Serialize;
use serde_json::{self, Value};
use std::error::Error;
use std::fmt;

#[derive(Debug)]
pub enum FormatterError {
    SerializationError(serde_json::Error),
    InvalidFormat(String),
}

impl fmt::Display for FormatterError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::SerializationError(e) => write!(f, "Serialization error: {}", e),
            Self::InvalidFormat(msg) => write!(f, "Invalid format: {}", msg),
        }
    }
}

impl Error for FormatterError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        match self {
            Self::SerializationError(e) => Some(e),
            Self::InvalidFormat(_) => None,
        }
    }
}

impl From<serde_json::Error> for FormatterError {
    fn from(err: serde_json::Error) -> Self {
        Self::SerializationError(err)
    }
}

/// Base trait for formatting output
pub trait FormatOutput: Send + Sync {
    fn format_value(&self, value: &Value) -> Result<String, FormatterError>;
    fn format_error(&self, error: &ErrorOutput) -> Result<String, FormatterError>;
    fn format_generic(&self, output: &GenericOutput) -> Result<String, FormatterError>;
}

/// JSON formatter implementation
pub struct JsonFormatter;

impl FormatOutput for JsonFormatter {
    fn format_value(&self, value: &Value) -> Result<String, FormatterError> {
        Ok(serde_json::to_string_pretty(value)?)
    }

    fn format_error(&self, error: &ErrorOutput) -> Result<String, FormatterError> {
        Ok(serde_json::to_string_pretty(error)?)
    }

    fn format_generic(&self, output: &GenericOutput) -> Result<String, FormatterError> {
        Ok(serde_json::to_string_pretty(output)?)
    }
}

/// Text formatter implementation
pub struct TextFormatter {
    verbose: bool,
}

/// Human-readable formatter for improved user experience
pub struct HumanFormatter {
    verbose: bool,
    labels: FieldLabels,
}

impl TextFormatter {
    pub fn new(verbose: bool) -> Self {
        Self { verbose }
    }

    fn format_data_value(&self, output: &mut String, value: &Value, indent: usize) {
        let indent_str = " ".repeat(indent);
        match value {
            Value::Null => output.push_str("null\n"),
            Value::Bool(b) => output.push_str(&format!("{}\n", b)),
            Value::Number(n) => output.push_str(&format!("{}\n", n)),
            Value::String(s) => output.push_str(&format!("{}\n", s)),
            Value::Array(arr) => {
                if arr.is_empty() {
                    output.push_str("[]\n");
                    return;
                }
                output.push_str("[\n");
                for item in arr.iter() {
                    output.push_str(&format!("{}  ", indent_str));
                    self.format_data_value(output, item, indent + 2);
                }
                output.push_str(&format!("{}]\n", indent_str));
            }
            Value::Object(obj) => {
                if obj.is_empty() {
                    output.push_str("{}\n");
                    return;
                }
                if !self.verbose {
                    // In non-verbose mode, show compact object
                    output.push_str(&format!("{}{{\n", indent_str));
                    for (key, val) in obj {
                        output.push_str(&format!(
                            "{}  {}: ",
                            indent_str,
                            key.blue().bold()
                        ));
                        match val {
                            Value::Object(obj) => {
                                // Show small objects (≤ 5 fields) or important status objects
                                if obj.len() <= 5 || key == "status" || key == "all_config" || key == "key_info" {
                                    output.push('\n');
                                    self.format_data_value(output, val, indent + 2);
                                } else {
                                    output.push_str("[...]\n");
                                }
                            }
                            Value::Array(arr) => {
                                // Show small arrays (≤ 3 elements) or always show if it's a config-related field
                                if arr.len() <= 3 || key == "config" || key == "keys" {
                                    output.push('\n');
                                    self.format_data_value(output, val, indent + 2);
                                } else {
                                    output.push_str("[...]\n");
                                }
                            }
                            _ => self.format_data_value(output, val, indent + 2),
                        }
                    }
                    output.push_str(&format!("{}}}\n", indent_str));
                } else {
                    // In verbose mode, show full object details
                    output.push_str(&format!("{}{{\n", indent_str));
                    for (key, val) in obj {
                        output.push_str(&format!(
                            "{}  {}: ",
                            indent_str,
                            key.blue().bold()
                        ));
                        self.format_data_value(output, val, indent + 2);
                    }
                    output.push_str(&format!("{}}}\n", indent_str));
                }
            }
        }
    }

    fn format_status(&self, output: &mut String, status: &OperationStatus) {
        output.push_str(&format!(
            "{}: {}\n",
            "Success".blue().bold(),
            if status.success {
                "true".green()
            } else {
                "false".red()
            }
        ));

        if let Some(msg) = &status.message {
            output.push_str(&format!("{}: {}\n", "Message".blue().bold(), msg));
        }

        if !status.code.is_empty() {
            output.push_str(&format!("{}: {}\n", "Code".blue().bold(), status.code));
        }

        if let Some(context) = &status.context {
            output.push_str(&format!("{}:\n", "Context".blue().bold()));
            self.format_data_value(output, context, 2);
        }
    }
}

impl FormatOutput for TextFormatter {
    fn format_value(&self, value: &Value) -> Result<String, FormatterError> {
        let mut result = String::new();
        self.format_data_value(&mut result, value, 0);
        Ok(result)
    }

    fn format_error(&self, error: &ErrorOutput) -> Result<String, FormatterError> {
        let mut result = String::new();
        result.push_str(&format!("\n{}\n", "Error:".red().bold()));
        result.push_str(&format!("{}: {}\n", "Code".red().bold(), error.code));
        result.push_str(&format!("{}: {}\n", "Message".red().bold(), error.message));

        if let Some(context) = &error.context {
            result.push_str(&format!("{}:\n", "Context".red().bold()));
            self.format_data_value(&mut result, context, 2);
        }
        Ok(result)
    }

    fn format_generic(&self, output: &GenericOutput) -> Result<String, FormatterError> {
        let mut result = String::new();
        result.push_str(&format!("\n{}\n", "Output:".green().bold()));

        self.format_status(&mut result, &output.status);

        if let Some(data) = &output.data {
            result.push_str(&format!("{}:\n", "Data".blue().bold()));
            self.format_data_value(&mut result, data, 2);
        }

        Ok(result)
    }
}

impl HumanFormatter {
    pub fn new(verbose: bool) -> Self {
        Self {
            verbose,
            labels: FieldLabels::new(),
        }
    }

    fn format_data_value(&self, output: &mut String, value: &Value, indent: usize) {
        let indent_str = " ".repeat(indent);
        match value {
            Value::Null => output.push_str("None\n"),
            Value::Bool(b) => output.push_str(&format!("{}\n", if *b { "Yes" } else { "No" })),
            Value::Number(n) => output.push_str(&format!("{}\n", n)),
            Value::String(s) => output.push_str(&format!("{}\n", s)),
            Value::Array(arr) => {
                if arr.is_empty() {
                    output.push_str("None\n");
                    return;
                }
                for (i, item) in arr.iter().enumerate() {
                    if i > 0 {
                        output.push('\n');
                    }
                    output.push_str(&format!("{}• ", indent_str));
                    self.format_data_value(output, item, indent + 2);
                }
            }
            Value::Object(obj) => {
                if obj.is_empty() {
                    output.push_str("None\n");
                    return;
                }

                let mut first_item = true;
                for (key, val) in obj {
                    if self.labels.should_hide_field(key) {
                        continue;
                    }

                    let label = self.labels.get_label(key);

                    if self.labels.is_section_header(key) {
                        // Section headers
                        if !first_item {
                            output.push('\n');
                        }
                        output.push_str(&format!("{}{}:\n", indent_str, label.blue().bold()));
                        self.format_data_value(output, val, indent + 2);
                    } else {
                        // Regular fields
                        match val {
                            Value::Object(_) | Value::Array(_) => {
                                output.push_str(&format!("{}{}: ", indent_str, label.blue()));
                                self.format_data_value(output, val, indent + 2);
                            }
                            Value::String(s) if self.is_long_string(s) => {
                                // For long strings (like addresses), put them on a new line
                                output.push_str(&format!("{}{}:\n{}{}\n", indent_str, label.blue(), indent_str, s));
                            }
                            _ => {
                                output.push_str(&format!("{}{}: ", indent_str, label.blue()));
                                self.format_data_value(output, val, 0);
                            }
                        }
                    }
                    first_item = false;
                }
            }
        }
    }
}

impl FormatOutput for HumanFormatter {
    fn format_value(&self, value: &Value) -> Result<String, FormatterError> {
        let mut result = String::new();
        self.format_data_value(&mut result, value, 0);
        Ok(result)
    }

    fn format_error(&self, error: &ErrorOutput) -> Result<String, FormatterError> {
        let mut result = String::new();
        result.push_str(&format!("{}\n", "Error".red().bold()));
        result.push_str(&format!("  Code: {}\n", error.code));
        result.push_str(&format!("  Message: {}\n", error.message));

        if let Some(context) = &error.context {
            result.push_str(&format!("\n{}:\n", "Context".blue().bold()));
            self.format_data_value(&mut result, context, 2);
        }
        Ok(result)
    }

    fn format_generic(&self, output: &GenericOutput) -> Result<String, FormatterError> {
        let mut result = String::new();

        if output.status.success {
            result.push_str(&format!("{}\n", "Success".green().bold()));
        } else {
            result.push_str(&format!("{}\n", "Failed".red().bold()));
        }

        if let Some(msg) = &output.status.message {
            result.push_str(&format!("Message: {}\n", msg));
        }

        if let Some(data) = &output.data {
            result.push('\n');
            self.format_data_value(&mut result, data, 0);
        }

        Ok(result)
    }
}

/// Output formatter for CLI commands
pub struct OutputFormatter {
    format: Box<dyn FormatOutput>,
}

impl std::fmt::Debug for OutputFormatter {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("OutputFormatter")
            .field("format", &"<dyn FormatOutput>")
            .finish()
    }
}

impl Clone for OutputFormatter {
    fn clone(&self) -> Self {
        // Create a new default formatter
        // Since we cannot directly access internal state, we use the default formatter
        Self::new(OutputFormat::Text)
    }
}

impl OutputFormatter {
    /// Create a new output formatter with the specified format
    pub fn new(format: OutputFormat) -> Self {
        let format: Box<dyn FormatOutput> = match format {
            OutputFormat::Json => Box::new(JsonFormatter),
            OutputFormat::Text => Box::new(TextFormatter::new(false)),
            OutputFormat::TextVerbose => Box::new(TextFormatter::new(true)),
            OutputFormat::Human => Box::new(HumanFormatter::new(false)),
            OutputFormat::HumanVerbose => Box::new(HumanFormatter::new(true)),
        };
        Self { format }
    }

    /// Create from boolean flags (for backwards compatibility)
    pub fn from_flags(is_json: bool, verbose: bool) -> Self {
        Self::new(OutputFormat::from_flags(is_json, verbose))
    }

    /// Create human-friendly formatter
    pub fn human(verbose: bool) -> Self {
        Self::new(if verbose { OutputFormat::HumanVerbose } else { OutputFormat::Human })
    }

    pub fn format<T: Serialize>(&self, output: &T) -> Result<String, FormatterError> {
        let value = serde_json::to_value(output)?;
        self.format.format_value(&value)
    }

    pub fn format_error(&self, error: &ErrorOutput) -> Result<String, FormatterError> {
        self.format.format_error(error)
    }

    pub fn format_generic(
        &self,
        output: &GenericOutput,
    ) -> Result<String, FormatterError> {
        self.format.format_generic(output)
    }

    // Command-specific formatters that delegate to the generic format method
    pub fn format_call(&self, output: &CallOutput) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_deploy(&self, output: &DeployOutput) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_query(&self, output: &QueryOutput) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_upgrade(
        &self,
        output: &UpgradeOutput,
    ) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_fork(&self, output: &ForkOutput) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_receipt(
        &self,
        output: &ReceiptOutput,
    ) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_config(&self, output: &ConfigOutput) -> Result<String, FormatterError> {
        self.format(output)
    }

    pub fn format_key(&self, output: &KeyOutput) -> Result<String, FormatterError> {
        self.format(output)
    }

    // Additional formatters for specific commands
    pub fn format_new(&self, output: NewCommandOutput) -> String {
        match self.format(&output) {
            Ok(result) => result,
            Err(_) => format!("Error formatting new command output: {:?}", output),
        }
    }

    pub fn format_version(&self, output: VersionOutput) -> String {
        match self.format(&output) {
            Ok(result) => result,
            Err(_) => format!("Error formatting version output: {:?}", output),
        }
    }
}
