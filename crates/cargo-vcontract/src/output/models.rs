//! Output data models for cargo-vcontract commands
//!
//! This module defines standardized output structures for each command,
//! enabling consistent data representation across different output formats.

use serde::{Deserialize, Serialize};
use serde_json::Value;

/// Common result status for all operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperationStatus {
    pub success: bool,
    pub message: Option<String>,
    pub code: String,
    pub context: Option<Value>,
}

impl OperationStatus {
    pub fn success(message: Option<String>) -> Self {
        Self {
            success: true,
            message,
            code: "SUCCESS".to_string(),
            context: None,
        }
    }

    pub fn error(code: String, message: String) -> Self {
        Self {
            success: false,
            message: Some(message),
            code,
            context: None,
        }
    }

    pub fn with_context(mut self, context: Value) -> Self {
        self.context = Some(context);
        self
    }
}

/// Generic output structure for simple operations
#[derive(Debug, Serialize, Deserialize)]
pub struct GenericOutput {
    pub status: OperationStatus,
    pub data: Option<Value>,
}

/// Base trait for all command outputs
pub trait CommandOutput {
    /// Validate the output data
    fn validate(&self) -> Result<(), String>;
    /// Convert to a command result
    fn into_result(self) -> CommandResult<Self>
    where
        Self: Sized;
}

/// Generic command result wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct CommandResult<T> {
    pub status: OperationStatus,
    pub data: Option<T>,
}

/// Output structure for build command
#[derive(Debug, Serialize, Deserialize)]
pub struct BuildOutput {
    pub contract_file: Option<String>,
    pub metadata_file: Option<String>,
    pub wasm_file: Option<String>,
    pub contract_size: Option<u64>,
    pub optimization_info: Option<String>,
}

impl CommandOutput for BuildOutput {
    fn validate(&self) -> Result<(), String> {
        if self.contract_file.is_none() && self.wasm_file.is_none() {
            return Err("Either contract_file or wasm_file must be present".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus::success(Some(
                "Build completed successfully".to_string(),
            )),
            data: Some(self),
        }
    }
}

/// Output structure for deploy command
#[derive(Debug, Serialize, Deserialize)]
pub struct DeployOutput {
    pub status: OperationStatus,
    pub transaction_hash: String,
    pub contract_address: String,
    pub code_hash: String,
    pub contract_name: String,
    pub fuel_consumed: Option<u64>,
    pub receipt: Option<Value>,
}

impl CommandOutput for DeployOutput {
    fn validate(&self) -> Result<(), String> {
        if self.transaction_hash.is_empty() {
            return Err("Transaction hash cannot be empty".to_string());
        }
        if self.contract_address.is_empty() {
            return Err("Contract address cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus::success(Some(
                "Contract deployed successfully".to_string(),
            )),
            data: Some(self),
        }
    }
}

/// Output structure for call command
#[derive(Debug, Serialize, Deserialize)]
pub struct CallOutput {
    pub status: OperationStatus,
    pub transaction_hash: String,
    pub function_name: String,
    pub result: Option<Value>,
    pub fuel_consumed: Option<u64>,
    pub receipt: Option<Value>,
}

impl CommandOutput for CallOutput {
    fn validate(&self) -> Result<(), String> {
        if self.transaction_hash.is_empty() {
            return Err("Transaction hash cannot be empty".to_string());
        }
        if self.function_name.is_empty() {
            return Err("Function name cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus::success(Some(format!(
                "Successfully called function '{}'",
                self.function_name
            ))),
            data: Some(self),
        }
    }
}

/// Output structure for query command
#[derive(Debug, Serialize, Deserialize)]
pub struct QueryOutput {
    pub status: OperationStatus,
    pub function_name: String,
    pub result: Option<Value>,
}

impl CommandOutput for QueryOutput {
    fn validate(&self) -> Result<(), String> {
        if self.function_name.is_empty() {
            return Err("Function name cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus::success(Some(format!(
                "Successfully queried function '{}'",
                self.function_name
            ))),
            data: Some(self),
        }
    }
}

/// Output structure for upgrade command
#[derive(Debug, Serialize, Deserialize)]
pub struct UpgradeOutput {
    pub status: OperationStatus,
    pub transaction_hash: String,
    pub contract_address: String,
    pub old_code_hash: String,
    pub new_code_hash: String,
    pub fuel_consumed: Option<u64>,
    pub receipt: Option<Value>,
}

impl CommandOutput for UpgradeOutput {
    fn validate(&self) -> Result<(), String> {
        if self.transaction_hash.is_empty() {
            return Err("Transaction hash cannot be empty".to_string());
        }
        if self.contract_address.is_empty() {
            return Err("Contract address cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus::success(Some(
                "Contract upgraded successfully".to_string(),
            )),
            data: Some(self),
        }
    }
}

/// Output structure for fork command
#[derive(Debug, Serialize, Deserialize)]
pub struct ForkOutput {
    pub status: OperationStatus,
    pub original_transaction_hash: String,
    pub new_transaction_hash: String,
    pub new_contract_address: Option<String>,
    pub fuel_consumed: Option<u64>,
    pub receipt: Option<Value>,
}

impl CommandOutput for ForkOutput {
    fn validate(&self) -> Result<(), String> {
        if self.original_transaction_hash.is_empty() {
            return Err("Original transaction hash cannot be empty".to_string());
        }
        if self.new_transaction_hash.is_empty() {
            return Err("New transaction hash cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: true,
                message: Some("Contract forked successfully".to_string()),
                code: "SUCCESS".to_string(),
                context: None,
            },
            data: Some(self),
        }
    }
}

/// Output structure for receipt command
#[derive(Debug, Serialize, Deserialize)]
pub struct ReceiptOutput {
    pub status: OperationStatus,
    pub transaction_hash: String,
    pub receipt: Value,
    pub block_number: Option<u64>,
    pub timestamp: Option<String>,
}

impl CommandOutput for ReceiptOutput {
    fn validate(&self) -> Result<(), String> {
        if self.transaction_hash.is_empty() {
            return Err("Transaction hash cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: true,
                message: Some("Receipt retrieved successfully".to_string()),
                code: "SUCCESS".to_string(),
                context: None,
            },
            data: Some(self),
        }
    }
}

/// Output structure for config command
#[derive(Debug, Serialize, Deserialize)]
pub struct ConfigOutput {
    pub status: OperationStatus,
    pub action: String,
    pub config_key: Option<String>,
    pub config_value: Option<String>,
    pub all_config: Option<Value>,
}

impl CommandOutput for ConfigOutput {
    fn validate(&self) -> Result<(), String> {
        if self.action.is_empty() {
            return Err("Action cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: true,
                message: Some(format!("Config {} completed successfully", self.action)),
                code: "SUCCESS".to_string(),
                context: None,
            },
            data: Some(self),
        }
    }
}

/// Output structure for key command
#[derive(Debug, Serialize, Deserialize)]
pub struct KeyOutput {
    pub status: OperationStatus,
    pub action: String,
    pub key_info: Option<Value>,
    pub message: Option<String>,
}

impl CommandOutput for KeyOutput {
    fn validate(&self) -> Result<(), String> {
        if self.action.is_empty() {
            return Err("Action cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: true,
                message: Some(format!("Key {} completed successfully", self.action)),
                code: "SUCCESS".to_string(),
                context: None,
            },
            data: Some(self),
        }
    }
}

/// Output structure for new command
#[derive(Debug, Serialize, Deserialize)]
pub struct NewCommandOutput {
    pub status: OperationStatus,
    pub project_name: String,
    pub target_directory: Option<String>,
    pub template_used: Option<String>,
}

impl CommandOutput for NewCommandOutput {
    fn validate(&self) -> Result<(), String> {
        if self.project_name.is_empty() {
            return Err("Project name cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: true,
                message: Some(format!(
                    "Project '{}' created successfully",
                    self.project_name
                )),
                code: "SUCCESS".to_string(),
                context: None,
            },
            data: Some(self),
        }
    }
}

/// Output structure for version command
#[derive(Debug, Serialize, Deserialize)]
pub struct VersionOutput {
    pub status: OperationStatus,
    pub version: String,
    pub package_name: String,
    pub description: String,
    pub build_info: Option<String>,
}

impl CommandOutput for VersionOutput {
    fn validate(&self) -> Result<(), String> {
        if self.version.is_empty() {
            return Err("Version cannot be empty".to_string());
        }
        if self.package_name.is_empty() {
            return Err("Package name cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: true,
                message: Some(format!("{} version {}", self.package_name, self.version)),
                code: "SUCCESS".to_string(),
                context: None,
            },
            data: Some(self),
        }
    }
}

/// Error output structure
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorOutput {
    pub code: String,
    pub message: String,
    pub context: Option<Value>,
}

impl CommandOutput for ErrorOutput {
    fn validate(&self) -> Result<(), String> {
        if self.code.is_empty() {
            return Err("Error code cannot be empty".to_string());
        }
        if self.message.is_empty() {
            return Err("Error message cannot be empty".to_string());
        }
        Ok(())
    }

    fn into_result(self) -> CommandResult<Self> {
        CommandResult {
            status: OperationStatus {
                success: false,
                message: Some(self.message.clone()),
                code: self.code.clone(),
                context: self.context.clone(),
            },
            data: Some(self),
        }
    }
}
