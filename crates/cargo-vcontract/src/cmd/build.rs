use crate::cmd::traits::CommandOutput;
use crate::output::{BuildOutput, CommandResult, OperationStatus, OutputFormatter};
use crate::services::ServiceResult;
use clap::Args;
use contract_build::{
    BuildArtifacts, BuildMode, BuildResult, ExecuteArgs, ManifestPath,
    OptimizationPasses, OutputType, Verbosity as ContractBuildVerbosity, VerbosityFlags,
};
use serde::Serialize;
use std::path::PathBuf;
// Removed unused imports: Arc, Mutex, Runtime

/// Executes build of the smart contract which produces a Wasm binary that is ready for
/// deploying.
///
/// It does so by invoking `cargo build` and then post processing the final binary.
#[derive(Debug, Args)]
#[clap(name = "build")]
pub struct BuildCommand {
    /// Path to Cargo.toml
    #[clap(long, default_value = "Cargo.toml")]
    pub manifest_path: String,

    /// Directory for all generated artifacts
    #[clap(long)]
    pub target_dir: Option<String>,

    /// Number of optimization passes
    #[clap(long)]
    pub optimization_passes: Option<u32>,

    /// Skip validation of the generated Wasm
    #[clap(long)]
    pub skip_wasm_validation: bool,

    /// By default the contract is compiled with debug functionality
    /// included. This enables the contract to output debug messages,
    /// but increases the contract size and the amount of gas used.
    ///
    /// A production contract should always be build in `release` mode!
    /// Then no debug functionality is compiled into the contract.
    #[clap(long)]
    pub release: bool,

    /// Which build artifacts to generate.
    ///
    /// - `all`: Generate the Wasm, the metadata and a bundled `<n>.contract` file.
    ///
    /// - `code-only`: Only the Wasm is created, generation of metadata and a bundled
    ///   `<n>.contract` file is skipped.
    #[clap(long, default_value = "all")]
    pub build_artifact: String,

    #[clap(flatten)]
    pub verbosity: VerbosityFlags,

    /// Do not remove symbols (Wasm name section) when optimizing.
    ///
    /// This is useful if one wants to analyze or debug the optimized binary.
    #[clap(long)]
    pub keep_debug_symbols: bool,
}

impl Default for BuildCommand {
    fn default() -> Self {
        Self {
            manifest_path: "Cargo.toml".to_string(),
            target_dir: None,
            optimization_passes: None,
            skip_wasm_validation: false,
            release: false,
            build_artifact: "all".to_string(),
            verbosity: VerbosityFlags::default(),
            keep_debug_symbols: false,
        }
    }
}

impl BuildCommand {
    pub fn exec(&self) -> ServiceResult<CommandResult<BuildOutput>> {
        let result = self.exec_build()?;
        self.process_result(result)
    }

    fn exec_build(&self) -> ServiceResult<BuildResult> {
        let manifest_path = PathBuf::from(&self.manifest_path);

        let verbosity = match (self.verbosity.is_json(), self.verbosity.is_verbose()) {
            (true, _) => ContractBuildVerbosity::Json,
            (false, true) => ContractBuildVerbosity::Verbose,
            (false, false) => ContractBuildVerbosity::Normal,
        };

        let build_mode = if self.release {
            BuildMode::Release
        } else {
            BuildMode::Debug
        };

        let output_type = if self.verbosity.is_json() {
            OutputType::Json
        } else {
            OutputType::HumanReadable
        };

        // Parse build artifact type
        let build_artifact = match self.build_artifact.as_str() {
            "all" => BuildArtifacts::All,
            "code-only" => BuildArtifacts::CodeOnly,
            _ => BuildArtifacts::All, // Default to All if not specified or invalid
        };

        // Create optimization passes if specified
        let optimization_passes = self.optimization_passes.map(|p| {
            // Convert u32 to string, then use FromStr trait
            let p_str = p.to_string();
            OptimizationPasses::from(p_str)
        });

        let args = ExecuteArgs {
            manifest_path: ManifestPath::new(manifest_path)
                .unwrap_or_else(|_| ManifestPath::default()),
            verbosity,
            build_mode,
            output_type,
            build_artifact,
            optimization_passes,
            keep_debug_symbols: self.keep_debug_symbols,
        };

        Ok(contract_build::execute(args)?)
    }

    fn process_result(
        &self,
        result: BuildResult,
    ) -> ServiceResult<CommandResult<BuildOutput>> {
        let dest_wasm = result.dest_wasm.clone();
        let output = BuildOutput {
            status: OperationStatus {
                success: true,
                message: Some("Build completed successfully".to_string()),
                code: "BUILD_SUCCESS".to_string(),
                context: None,
            },
            contract_file: result
                .metadata_result
                .as_ref()
                .map(|m| m.dest_bundle.to_string_lossy().into_owned()),
            metadata_file: result
                .metadata_result
                .map(|m| m.dest_metadata.to_string_lossy().into_owned()),
            wasm_file: dest_wasm.map(|p| p.to_string_lossy().into_owned()),
            contract_size: None,
            optimization_info: result.optimization_result.map(|r| {
                format!(
                    "Original size: {:.1}K, Optimized: {:.1}K",
                    r.original_size, r.optimized_size
                )
            }),
        };

        Ok(CommandResult {
            status: output.status.clone(),
            data: Some(output),
        })
    }

    fn format_output<T: Serialize>(&self, data: T) -> String {
        let formatter = OutputFormatter::from_flags(self.is_json(), self.is_verbose());
        match formatter.format(&data) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        }
    }
}

impl CommandOutput for BuildCommand {
    type Output = BuildOutput;

    fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    fn is_verbose(&self) -> bool {
        self.verbosity.is_verbose()
    }

    fn exec(&self) -> ServiceResult<()> {
        let build_result = self.exec_build()?;
        let command_result = self.process_result(build_result)?;
        let formatter = OutputFormatter::from_flags(self.is_json(), self.is_verbose());
        match formatter.format(&command_result) {
            Ok(output) => println!("{}", output),
            Err(e) => println!("Error formatting output: {}", e),
        }
        Ok(())
    }

    fn format_output<T: Serialize>(&self, data: T) -> String {
        let formatter = OutputFormatter::from_flags(self.is_json(), self.is_verbose());
        match formatter.format(&data) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        }
    }
}

impl BuildCommand {
    /// Execute the actual build process (internal method)
    async fn async_exec(&self) -> ServiceResult<()> {
        // TODO: Implement actual build logic
        let output = BuildOutput {
            status: OperationStatus {
                success: true,
                message: Some("Build completed successfully".to_string()),
                code: "BUILD_SUCCESS".to_string(),
                context: None,
            },
            contract_file: Some("contract.wasm".to_string()),
            metadata_file: Some("metadata.json".to_string()),
            wasm_file: Some("contract.wasm".to_string()),
            contract_size: Some(1024),
            optimization_info: Some("optimized".to_string()),
        };

        let result = CommandResult {
            status: output.status.clone(),
            data: Some(output),
        };

        println!("{}", self.format_output(result));

        Ok(())
    }
}
