// Output formatting functionality for cargo-vcontract
// This module handles formatting of success and error outputs

use serde_json::json;

/// Format error as JSON or plain text
pub fn format_error(error_msg: &str, json_output: bool) -> String {
    if json_output {
        let error_obj = json!({
            "error": error_msg,
            "success": false
        });
        serde_json::to_string(&error_obj)
            .unwrap_or_else(|_| format!(r#"{{"error":"{}","success":false}}"#, error_msg))
    } else {
        error_msg.to_string()
    }
}

/// Format function validation errors with additional context
#[allow(dead_code)]
pub fn format_validation_error(
    error_msg: &str,
    contract_address: &str,
    function_name: &str,
    command_type: &str,
    json_output: bool,
) -> String {
    if json_output {
        let error_obj = json!({
            "error": error_msg,
            "error_type": "function_validation_error",
            "contract_address": contract_address,
            "function_name": function_name,
            "command_type": command_type,
            "success": false
        });
        serde_json::to_string(&error_obj)
            .unwrap_or_else(|_| format!(r#"{{"error":"{}","success":false}}"#, error_msg))
    } else {
        error_msg.to_string()
    }
}
