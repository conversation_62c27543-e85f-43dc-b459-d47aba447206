use crate::services::{ServiceError, ServiceResult};
use clap::{Args, Subcommand};
use serde_json::json;

use crate::cmd::auth::{get_saved_address, is_logged_in};
use crate::output::{
    models::{ConfigOutput, OperationStatus},
    OutputFormatter,
};
use crate::services::ConfigService;

/// Manage VGraph configuration
#[derive(Debug, Args)]
#[clap(name = "config")]
pub struct ConfigCommand {
    #[clap(subcommand)]
    subcmd: ConfigSubcommand,
}

#[derive(Debug, Subcommand)]
enum ConfigSubcommand {
    /// Set default node URL
    #[clap(name = "set-node")]
    SetNode {
        /// Node URL (e.g., 127.0.0.1:9877)
        #[clap(value_parser)]
        url: String,
    },
    /// Show current configuration
    #[clap(name = "show")]
    Show,
    /// Reset configuration to defaults
    #[clap(name = "reset")]
    Reset,
}

impl ConfigCommand {
    pub fn exec(&self) -> ServiceResult<()> {
        let config_service = ConfigService::new();

        match &self.subcmd {
            ConfigSubcommand::SetNode { url } => {
                config_service.set_default_node(url.clone()).map_err(|e| {
                    ServiceError::internal(format!("Failed to set node URL: {}", e))
                })?;

                let formatter = OutputFormatter::from_flags(false, false); // Config command typically doesn't have JSON mode

                let config_output = ConfigOutput {
                    status: OperationStatus {
                        success: true,
                        message: Some(format!("Default node set to {}", url)),
                        code: "SUCCESS".to_string(),
                        context: None,
                    },
                    action: "set".to_string(),
                    config_key: Some("default_node".to_string()),
                    config_value: Some(url.clone()),
                    all_config: None,
                };

                match formatter.format(&config_output) {
                    Ok(output) => println!("{}", output),
                    Err(e) => println!("Error formatting output: {}", e),
                }
                Ok(())
            }
            ConfigSubcommand::Show => {
                let config = config_service.load_config().map_err(|e| {
                    ServiceError::internal(format!("Failed to load config: {}", e))
                })?;

                let config_file_path = config_service
                    .get_config_file_path()
                    .map_err(|e| {
                        ServiceError::internal(format!(
                            "Failed to get config path: {}",
                            e
                        ))
                    })?
                    .display()
                    .to_string();

                // Get authentication status
                let auth_status = if is_logged_in() {
                    if let Ok(address) = get_saved_address() {
                        format!("{} [OK]", address)
                    } else {
                        "Yes (address unavailable)".to_string()
                    }
                } else {
                    "No [FAIL]".to_string()
                };

                let formatter = OutputFormatter::from_flags(false, false);

                let all_config_data = json!({
                    "default_node": config.default_node,
                    "config_file": config_file_path,
                    "authentication": auth_status
                });

                let config_output = ConfigOutput {
                    status: OperationStatus {
                        success: true,
                        message: Some("Configuration retrieved successfully".to_string()),
                        code: "SUCCESS".to_string(),
                        context: None,
                    },
                    action: "list".to_string(),
                    config_key: None,
                    config_value: None,
                    all_config: Some(all_config_data),
                };

                match formatter.format(&config_output) {
                    Ok(output) => println!("{}", output),
                    Err(e) => println!("Error formatting output: {}", e),
                }
                Ok(())
            }
            ConfigSubcommand::Reset => {
                let default_config = config_service.reset_config()?;

                let formatter = OutputFormatter::from_flags(false, false);

                let config_output = ConfigOutput {
                    status: OperationStatus {
                        success: true,
                        message: Some("Configuration reset to defaults".to_string()),
                        code: "SUCCESS".to_string(),
                        context: None,
                    },
                    action: "reset".to_string(),
                    config_key: Some("default_node".to_string()),
                    config_value: Some(default_config.default_node.clone()),
                    all_config: None,
                };

                match formatter.format(&config_output) {
                    Ok(output) => println!("{}", output),
                    Err(e) => println!("Error formatting output: {}", e),
                }
                Ok(())
            }
        }
    }
}

/// Get node URL, with fallback to config and default
/// This function is kept for backward compatibility
pub fn get_node_url(cmd_node: &str) -> ServiceResult<String> {
    let config_service = ConfigService::new();
    config_service.get_node_url(cmd_node)
}
