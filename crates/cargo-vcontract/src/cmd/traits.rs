// Removed unused import: OutputFormatter
use crate::services::ServiceResult;
use serde::Serialize;

/// Base trait for command output handling
pub trait CommandOutput {
    type Output: Serialize;

    fn is_json(&self) -> bool;
    fn is_verbose(&self) -> bool;
    fn exec(&self) -> ServiceResult<()>;
    fn format_output<T: Serialize>(&self, data: T) -> String;
}

pub trait Command {
    fn execute(&self) -> ServiceResult<()>;
}
