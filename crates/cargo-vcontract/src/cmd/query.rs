use clap::{Args, Subcommand};
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde::Serialize;
use serde_json::json;
use std::convert::TryFrom;
use std::path::PathBuf;
use tokio::runtime::Runtime;

use crate::cmd::utils::{format_error, list_contract_functions_with_context};
use crate::cmd::CommandOutput;
use crate::output::{models::*, OutputFormatter};
use crate::services::{
    ConfigService, MetadataService, NetworkService, ServiceError, ServiceResult,
    ValidationService,
};

/// Query a smart contract function (read-only)
#[derive(Debug, Args)]
#[clap(name = "query")]
pub struct QueryCommand {
    #[clap(subcommand)]
    subcmd: Option<QuerySubcommand>,

    /// Contract address (required for execute)
    #[clap(value_parser)]
    contract_address: Option<String>,

    /// Function name to query (required for execute)
    #[clap(value_parser)]
    function_name: Option<String>,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Function arguments (in JSON format)
    #[clap(long, short = 'a', num_args = 0..)]
    args: Vec<String>,

    /// Snapshot transaction hash (query state on top of this pending transaction)
    #[clap(long)]
    snapshot_tx: Option<String>,

    /// Query API key (optional)
    #[clap(long)]
    api_key: Option<String>,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

#[derive(Debug, Subcommand)]
enum QuerySubcommand {
    /// List available readonly functions in a contract
    #[clap(name = "ls")]
    List {
        /// Path to contract file (.contract file)
        #[clap(value_parser)]
        contract_file: Option<PathBuf>,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },
}

impl CommandOutput for QueryCommand {
    type Output = QueryOutput;

    fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    fn is_verbose(&self) -> bool {
        self.verbosity.is_verbose()
    }

    fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new().map_err(|e| {
            ServiceError::internal(format!("Failed to create tokio runtime: {}", e))
        })?;
        runtime.block_on(self.async_exec())
    }

    fn format_output<T: Serialize>(&self, data: T) -> String {
        let formatter =
            OutputFormatter::from_flags(self.is_json(), self.verbosity.is_verbose());

        // Try to convert the data to JSON Value first
        let value = serde_json::to_value(&data).unwrap_or_default();

        // If the value matches the structure of QueryOutput, convert it
        if let Ok(query_output) = serde_json::from_value::<QueryOutput>(value.clone()) {
            match formatter.format_query(&query_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting query output: {}", e),
            }
        } else {
            // Otherwise use generic formatting
            let generic_output = GenericOutput {
                status: OperationStatus {
                    success: true,
                    message: None,
                    code: "SUCCESS".to_string(),
                    context: None,
                },
                data: Some(value),
            };

            match formatter.format_generic(&generic_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting output: {}", e),
            }
        }
    }
}

impl QueryCommand {
    async fn async_exec(&self) -> ServiceResult<()> {
        // Initialize services
        let config_service = ConfigService::new();
        let validation_service = ValidationService::new();
        let metadata_service = MetadataService::new();
        let network_service = NetworkService::new();

        // Parse verbosity settings
        let verbosity = contract_build::Verbosity::try_from(&self.verbosity)
            .map_err(|e| ServiceError::internal(e.to_string()))?;
        let verbose = verbosity.is_verbose();
        let is_json = verbosity.is_json();

        // Handle subcommands
        if let Some(subcmd) = &self.subcmd {
            return match subcmd {
                QuerySubcommand::List {
                    contract_file,
                    verbosity: sub_verbosity,
                } => {
                    let sub_verbosity =
                        match contract_build::Verbosity::try_from(sub_verbosity) {
                            Ok(v) => v,
                            Err(e) => {
                                let error_msg = e.to_string();
                                if sub_verbosity.is_json() {
                                    println!("{}", format_error(&error_msg, true));
                                    return Ok(());
                                } else {
                                    return Err(ServiceError::internal(e.to_string()));
                                }
                            }
                        };
                    let sub_verbose = sub_verbosity.is_verbose();

                    let contract_file = match contract_file {
                        Some(path) => path.clone(),
                        None => {
                            match metadata_service
                                .find_contract_file_for_deploy_verbose(sub_verbose)
                            {
                                Ok(path) => path,
                                Err(e) => {
                                    return Err(ServiceError::internal(e.to_string()))
                                }
                            }
                        }
                    };
                    Ok(list_contract_functions_with_context(
                        &contract_file,
                        sub_verbose,
                        "query",
                        sub_verbosity.is_json(),
                    )
                    .map_err(|e| ServiceError::internal(e.to_string()))?)
                }
            };
        }

        // Step 1: Validate required parameters
        let contract_address = match self.contract_address.as_ref() {
            Some(addr) => addr,
            None => {
                return Err(ServiceError::validation(
                    "Contract address is required. Usage: cargo vcontract query <contract_address> <function_name>".to_string()
                ));
            }
        };

        let function_name = match self.function_name.as_ref() {
            Some(name) => name,
            None => {
                return Err(ServiceError::validation(
                    "Function name is required. Usage: cargo vcontract query <contract_address> <function_name>".to_string()
                ));
            }
        };

        // Step 2: Get node URL from configuration
        let node_url = config_service
            .get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;

        // Step 3: Connect to network
        let client = network_service.connect_to_node(&node_url).await?;

        // Step 4: Parse and validate arguments
        let args = match metadata_service.find_contract_file_for_deploy_verbose(false) {
            Ok(contract_file) => {
                // Set current contract address for parameter display
                std::env::set_var("VCONTRACT_CURRENT_ADDRESS", &contract_address);

                validation_service.validate_types(
                    &self.args,
                    &contract_file,
                    false,
                    Some(function_name),
                    is_json,
                )?
            }
            Err(_) => {
                // If no contract file found, use basic JSON validation
                validation_service.validate_json_args(&self.args)?
            }
        };

        // Step 5: Build and execute query
        if verbose && !is_json {
            println!("{}", "Building contract query...".blue());
        }

        // Ensure contract address has correct format
        let contract_address = if !contract_address.starts_with("0x") {
            format!("0x{}", contract_address)
        } else {
            contract_address.clone()
        };

        if verbose && !is_json {
            println!("{}", "Executing contract query...".blue());
        }

        let response = match client
            .query_contract(
                &contract_address,
                function_name,
                args,
                self.snapshot_tx.clone(),
                self.api_key.clone(),
            )
            .await
        {
            Ok(resp) => resp,
            Err(e) => {
                let error_msg = format!("Failed to query contract: {}", e);
                if is_json {
                    println!("{}", format_error(&error_msg, true));
                    return Ok(());
                } else {
                    return Err(ServiceError::network(error_msg));
                }
            }
        };

        // Step 6: Process and display results
        let has_error = response.get("error").is_some()
            || response
                .get("result")
                .and_then(|r| r.get("error"))
                .is_some();

        let actual_result = response.get("result").cloned().unwrap_or(response.clone());
        let success = !has_error;

        // Create QueryOutput using our new output structure
        let query_output = QueryOutput {
            status: OperationStatus {
                success,
                message: if success {
                    Some("Query executed successfully".to_string())
                } else {
                    Some("Query execution failed".to_string())
                },
                code: if success {
                    "SUCCESS".to_string()
                } else {
                    "QUERY_FAILED".to_string()
                },
                context: None,
            },
            function_name: function_name.to_string(),
            result: if success && actual_result != json!(null) {
                Some(actual_result)
            } else {
                None
            },
        };

        // Use our new format_output method
        println!("{}", self.format_output(query_output));

        Ok(())
    }
}
