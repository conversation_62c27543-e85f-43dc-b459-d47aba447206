use crate::services::{ServiceError, ServiceResult};
use clap::Args;
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde_json::Value;
use std::convert::TryFrom;
use tokio::runtime::Runtime;

use crate::cmd::config::get_node_url;
use crate::cmd::utils::connect_node;
use crate::output::{
    models::{OperationStatus, ReceiptOutput},
    OutputFormatter,
};

/// Query transaction receipt by hash
#[derive(Debug, Args)]
#[clap(name = "receipt")]
pub struct ReceiptCommand {
    /// Transaction hash to query
    #[clap(value_parser)]
    transaction_hash: String,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

impl ReceiptCommand {
    /// Check if JSON output is requested
    pub fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    pub fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new().map_err(|e| {
            ServiceError::internal(format!("Failed to create tokio runtime: {}", e))
        })?;
        runtime.block_on(self.async_exec())
    }

    async fn async_exec(&self) -> ServiceResult<()> {
        let verbosity = contract_build::Verbosity::try_from(&self.verbosity)
            .map_err(|e| ServiceError::internal(e.to_string()))?;
        let verbose = verbosity.is_verbose();

        // Connect to node
        let node_url = get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;
        let client = connect_node(&node_url, verbosity.is_json()).await?;

        // Query transaction receipt
        if verbose && !verbosity.is_json() {
            println!("Querying receipt for: {}", self.transaction_hash.yellow());
        }

        let receipt = client
            .get_transaction_receipt(&self.transaction_hash)
            .await
            .map_err(|e| {
                ServiceError::network(format!("Failed to get transaction receipt: {}", e))
            })?;

        // Check if receipt was found
        if receipt.is_null() {
            let error_msg = format!("Transaction not found: {}", self.transaction_hash);
            return Err(ServiceError::network(error_msg));
        }

        // Parse receipt for basic information
        let status = receipt
            .get("receipt")
            .and_then(|r| r.get("status"))
            .and_then(Value::as_bool)
            .unwrap_or(false);

        let tx_hash = receipt
            .get("receipt")
            .and_then(|r| r.get("transaction_hash"))
            .and_then(Value::as_str)
            .unwrap_or(&self.transaction_hash);

        let _block_hash = receipt
            .get("receipt")
            .and_then(|r| r.get("block_hash"))
            .and_then(Value::as_str)
            .unwrap_or("unknown");

        // Try to extract contract-related information
        let _contract_address = receipt
            .get("receipt")
            .and_then(|r| r.get("op_result"))
            .and_then(|op| op.get("contract_address"))
            .and_then(Value::as_str);

        let _op_type = receipt
            .get("receipt")
            .and_then(|r| r.get("op_result"))
            .and_then(|op| op.get("op_type"))
            .and_then(Value::as_i64);

        // Format and output result using unified output formatter
        let formatter = OutputFormatter::from_flags(verbosity.is_json(), verbose);

        let receipt_output = ReceiptOutput {
            status: OperationStatus {
                success: status,
                message: if status {
                    Some("Receipt retrieved successfully".to_string())
                } else {
                    Some("Transaction failed".to_string())
                },
                code: if status {
                    "SUCCESS".to_string()
                } else {
                    "FAILED".to_string()
                },
                context: None,
            },
            transaction_hash: tx_hash.to_string(),
            receipt,
            block_number: None, // Not directly available in current receipt structure
            timestamp: None,    // Not directly available in current receipt structure
        };

        match formatter.format(&receipt_output) {
            Ok(output) => println!("{}", output),
            Err(e) => println!("Error formatting output: {}", e),
        }

        Ok(())
    }
}
