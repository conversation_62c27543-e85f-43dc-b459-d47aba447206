use anyhow::Result;
use clap::Args;
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde::Serialize;
use serde_json::json;
use std::convert::TryFrom;

use std::path::PathBuf;
use tokio::runtime::Runtime;
// Removed unused imports since we use RPC directly

/// Represents a command line argument value that preserves type intention
#[derive(Debug, Clone)]
pub enum ArgValue {
    /// User explicitly wants a string (e.g., "string:100")
    ExplicitString(String),
    /// Regular argument that should be parsed normally
    Auto(String),
}

/// Custom parser for argument values that detects explicit string intention
fn parse_arg_value(s: &str) -> Result<ArgValue, String> {
    if let Some(value) = s.strip_prefix("string:") {
        Ok(ArgValue::ExplicitString(value.to_string()))
    } else {
        Ok(ArgValue::Auto(s.to_string()))
    }
}

use crate::cmd::utils::{display_constructor_arguments, wait_for_confirmation};
use crate::cmd::CommandOutput;
use crate::output::{models::*, OutputFormatter};
use crate::services::{
    AuthService, ConfigService, MetadataService, NetworkService, ServiceError,
    ServiceResult, ValidationService,
};

/// Deploy a smart contract to the VGraph network
#[derive(Debug, Args)]
#[clap(name = "deploy")]
pub struct DeployCommand {
    /// Path to contract file (.wasm or .contract file)
    #[clap(value_parser)]
    contract_file: Option<PathBuf>,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Contract initialization parameters (JSON format, or use 'string:value' for explicit strings)
    #[clap(long, short = 'a', num_args = 0.., value_parser = parse_arg_value)]
    args: Vec<ArgValue>,

    /// Contract name
    #[clap(long)]
    name: Option<String>,

    /// Contract description
    #[clap(long)]
    description: Option<String>,

    /// Make contract upgradable
    #[clap(long, default_value = "false")]
    upgradable: bool,

    /// Source code URL
    #[clap(long, default_value = "")]
    source_url: String,

    /// Git commit hash
    #[clap(long, default_value = "")]
    git_hash: String,

    /// Use reproducible build
    #[clap(long, default_value = "true")]
    reproducible: bool,

    /// Fuel value
    #[clap(long, default_value = "1000000")]
    fuel: u64,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

impl CommandOutput for DeployCommand {
    type Output = DeployOutput;

    fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    fn is_verbose(&self) -> bool {
        self.verbosity.is_verbose()
    }

    fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new().map_err(|e| {
            ServiceError::internal(format!("Failed to create tokio runtime: {}", e))
        })?;
        runtime.block_on(self.async_exec())
    }

    fn format_output<T: Serialize>(&self, data: T) -> String {
        let formatter =
            OutputFormatter::from_flags(self.is_json(), self.verbosity.is_verbose());

        // Try to convert the data to JSON Value first
        let value = serde_json::to_value(&data).unwrap_or_default();

        // If the value matches the structure of DeployOutput, convert it
        if let Ok(deploy_output) = serde_json::from_value::<DeployOutput>(value.clone()) {
            match formatter.format_deploy(&deploy_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting deploy output: {}", e),
            }
        } else {
            // Otherwise use generic formatting
            let generic_output = GenericOutput {
                status: OperationStatus {
                    success: true,
                    message: None,
                    code: "SUCCESS".to_string(),
                    context: None,
                },
                data: Some(value),
            };

            match formatter.format_generic(&generic_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting output: {}", e),
            }
        }
    }
}

impl DeployCommand {
    async fn async_exec(&self) -> ServiceResult<()> {
        // Initialize services
        let config_service = ConfigService::new();
        let validation_service = ValidationService::new();
        let metadata_service = MetadataService::new();
        let network_service = NetworkService::new();
        let auth_service = AuthService::new();

        // Parse verbosity settings
        let verbosity = contract_build::Verbosity::try_from(&self.verbosity)
            .map_err(|e| ServiceError::internal(e.to_string()))?;
        let verbose = verbosity.is_verbose();
        let is_json = verbosity.is_json();

        // Step 1: Find contract file
        let contract_file = match &self.contract_file {
            Some(path) => path.clone(),
            None => metadata_service.find_contract_file_for_deploy_verbose(verbose)?,
        };

        // Step 2: Validate fuel parameter
        validation_service.validate_fuel(self.fuel)?;

        // Step 3: Get node URL from configuration
        let node_url = config_service
            .get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;

        // Step 4: Connect to network
        let client = network_service.connect_to_node(&node_url).await?;

        // Step 5: Parse and validate arguments
        let args = if contract_file.extension().and_then(|ext| ext.to_str())
            == Some("contract")
        {
            // For .contract files, use type-aware parsing with metadata
            let args_vec: Vec<String> = self
                .args
                .iter()
                .map(|arg| match arg {
                    ArgValue::ExplicitString(s) => s.clone(),
                    ArgValue::Auto(s) => s.clone(),
                })
                .collect();

            validation_service.validate_types(
                &args_vec,
                &contract_file,
                true,
                None,
                is_json,
            )?
        } else {
            // For .wasm files, use smart argument parsing
            validation_service.parse_arguments_with_type_awareness(&self.args)?
        };

        // Step 6: Read contract bytecode
        if verbose && !is_json {
            println!(
                "Reading contract file: {}...",
                contract_file.display().to_string().blue()
            );
        }

        let contract_bytes = if contract_file.extension().and_then(|ext| ext.to_str())
            == Some("wasm")
        {
            // Using .wasm file
            if verbose && !is_json {
                println!(
                    "Reading WASM file: {}...",
                    contract_file.display().to_string().blue()
                );
                println!("No constructor parameter validation (using .wasm file without metadata)");
            }

            std::fs::read(&contract_file).map_err(|e| {
                ServiceError::file_system(format!(
                    "Failed to read WASM file {}: {}",
                    contract_file.display(),
                    e
                ))
            })?
        } else {
            // Using .contract file
            metadata_service.parse_contract_file(&contract_file)?
        };

        // Step 7: Display constructor arguments (UI layer responsibility)
        if !is_json && !args.is_empty() {
            if display_constructor_arguments(&contract_file, &args).is_err() {
                // If we can't display constructor arguments (e.g., for .wasm files), show simple args
                println!("{}:", "Constructor Arguments".green().bold());
                for (i, value) in args.iter().enumerate() {
                    println!(
                        "  {}: {}",
                        format!("arg{}", i).blue().bold(),
                        value.to_string().green()
                    );
                }
            }
        }

        // Step 8: Get authentication credentials
        let private_key = auth_service
            .get_saved_private_key()
            .map_err(|e| ServiceError::auth(e.to_string()))?;

        // Step 9: Build and submit transaction
        if verbose && !is_json {
            println!("{}", "Building contract creation transaction...".blue());
            println!(
                "{} Using fuel: {}",
                "[FUEL]".yellow(),
                self.fuel.to_string().green()
            );
        }

        let contract_name = self.name.clone().unwrap_or_else(|| {
            contract_file
                .file_stem()
                .map(|s| s.to_string_lossy().to_string())
                .unwrap_or_else(|| "Unnamed Contract".to_string())
        });

        let contract_hex = hex::encode(&contract_bytes);

        if verbose && !is_json {
            println!("{}", "Submitting transaction to network...".blue());
        }

        let params = json!([{
            "contract_hex_bytecode": contract_hex,
            "constructor_parameters": args,
            "contract_name": contract_name,
            "contract_description": self.description.clone().unwrap_or_default(),
            "contract_source_url": self.source_url.clone(),
            "upgradable": self.upgradable,
            "git_commit_hash": self.git_hash.clone(),
            "reproducible_build": self.reproducible,
            "fuel": self.fuel,
            "privatekey": private_key
        }]);

        let response = match client.request("contract.create", Some(params)).await {
            Ok(response) => response,
            Err(e) => {
                return Err(ServiceError::network(format!("RPC request failed: {}", e)));
            }
        };

        let tx_hash = response
            .get("transaction_hash")
            .and_then(|v| v.as_str())
            .ok_or_else(|| {
                ServiceError::network(format!(
                    "Invalid transaction hash response. Full response: {}",
                    response
                ))
            })?;

        // Step 10: Wait for transaction confirmation
        let receipt = wait_for_confirmation(&client, tx_hash, !is_json).await?;

        // Step 11: Process and display results
        let success = receipt["receipt"]["status"].as_bool().unwrap_or(false);

        if !success {
            let pretty_receipt = serde_json::to_string_pretty(&receipt)
                .unwrap_or_else(|_| receipt.to_string());
            return Err(ServiceError::contract(format!(
                "Contract creation failed. Full receipt:\n{}",
                pretty_receipt
            )));
        }

        let contract_address = receipt["receipt"]["op_result"]["contract_address"]
            .as_str()
            .ok_or_else(|| {
                ServiceError::contract(format!(
                    "Failed to get contract address from receipt. Full receipt: {}",
                    receipt
                ))
            })?;

        let code_hash = receipt["receipt"]["op_result"]["code_hash"]
            .as_str()
            .unwrap_or("unknown");

        let fuel_consumed = receipt
            .get("receipt")
            .and_then(|r| r.get("logs"))
            .and_then(|logs| logs.as_array())
            .and_then(|logs| {
                for log in logs {
                    if let Some(log_str) = log.as_str() {
                        if log_str.contains("Fuel consumed:") {
                            if let Some(fuel_part) =
                                log_str.split("Fuel consumed:").nth(1)
                            {
                                if let Ok(fuel_val) = fuel_part.trim().parse::<u64>() {
                                    return Some(fuel_val);
                                }
                            }
                        }
                    }
                }
                None
            })
            .unwrap_or(0);

        // Create DeployOutput using our new output structure
        let deploy_output = DeployOutput {
            status: OperationStatus {
                success: true,
                message: Some("Contract deployment successful".to_string()),
                code: "SUCCESS".to_string(),
                context: None,
            },
            transaction_hash: tx_hash.to_string(),
            contract_address: contract_address.to_string(),
            code_hash: code_hash.to_string(),
            contract_name,
            fuel_consumed: if fuel_consumed > 0 {
                Some(fuel_consumed)
            } else {
                None
            },
            receipt: if verbose { Some(receipt) } else { None },
        };

        // Use our new format_output method
        println!("{}", self.format_output(deploy_output));

        Ok(())
    }
}
