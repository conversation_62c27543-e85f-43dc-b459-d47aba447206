use crate::services::{ServiceError, ServiceResult};
use anyhow::{anyhow, Result}; // Keep for backward compatibility functions
use clap::{Args, Subcommand};
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde_json::json;
use std::collections::HashMap;
use std::fs;

use crate::output::{
    models::{KeyOutput, OperationStatus},
    OutputFormatter,
};

use std::time::{SystemTime, UNIX_EPOCH};

use vgraph_sdk_core::wallet::Wallet;

// get_key_file_path is now implemented locally below

/// Manage authentication keys for VGraph operations
#[derive(Debug, Args)]
#[clap(name = "key")]
pub struct KeyCommand {
    #[clap(subcommand)]
    subcmd: KeySubcommand,
}

#[derive(Debug, Subcommand)]
enum KeySubcommand {
    /// Add a named private key
    #[clap(name = "add")]
    Add {
        /// Name for this key (e.g., 'main', 'dev', 'test')
        #[clap(value_parser)]
        name: String,

        /// Private key in hex format (with or without 0x prefix)
        #[clap(value_parser)]
        private_key: String,

        /// Force overwrite existing key with same name
        #[clap(short = 'f', long)]
        force: bool,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },

    /// Remove a named key
    #[clap(name = "remove")]
    Remove {
        /// Name of the key to remove
        #[clap(value_parser)]
        name: String,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },

    /// List all saved keys
    #[clap(name = "ls")]
    Ls {
        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },

    /// Set the active key for contract operations
    #[clap(name = "use")]
    Use {
        /// Name of the key to activate
        #[clap(value_parser)]
        name: String,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },

    /// Generate new private key and optionally add it
    #[clap(name = "generate")]
    Generate {
        /// Name for the generated key
        #[clap(value_parser)]
        name: String,

        /// Use a seed phrase to generate deterministic keypair
        #[clap(short = 's', long)]
        seed: Option<String>,

        /// Automatically set this key as active
        #[clap(short = 'a', long)]
        activate: bool,

        /// Force overwrite existing key with same name
        #[clap(short = 'f', long)]
        force: bool,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },

    /// Show current key status and active key
    #[clap(name = "status")]
    Status {
        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Default)]
struct KeyStore {
    /// Map of key name to key data
    keys: HashMap<String, KeyData>,
    /// Currently active key name
    active_key: Option<String>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
struct KeyData {
    private_key: String,
    address: String,
    created_at: String,
}

fn get_current_timestamp() -> String {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
        .to_string()
}

impl KeyCommand {
    pub fn exec(&self) -> ServiceResult<()> {
        match &self.subcmd {
            KeySubcommand::Add {
                name,
                private_key,
                force,
                verbosity,
            } => Ok(self
                .exec_add(name, private_key, *force, verbosity.is_json(), verbosity)
                .map_err(|e| ServiceError::internal(e.to_string()))?),
            KeySubcommand::Remove { name, verbosity } => Ok(self
                .exec_remove(name, verbosity.is_json(), verbosity)
                .map_err(|e| ServiceError::internal(e.to_string()))?),
            KeySubcommand::Ls { verbosity } => Ok(self
                .exec_list(verbosity.is_json(), verbosity)
                .map_err(|e| ServiceError::internal(e.to_string()))?),
            KeySubcommand::Use { name, verbosity } => Ok(self
                .exec_use(name, verbosity.is_json(), verbosity)
                .map_err(|e| ServiceError::internal(e.to_string()))?),
            KeySubcommand::Generate {
                name,
                seed,
                activate,
                force,
                verbosity,
            } => Ok(self
                .exec_generate(
                    name,
                    seed.as_ref(),
                    *activate,
                    *force,
                    verbosity.is_json(),
                    verbosity,
                )
                .map_err(|e| ServiceError::internal(e.to_string()))?),
            KeySubcommand::Status { verbosity } => Ok(self
                .exec_status(verbosity.is_json(), verbosity)
                .map_err(|e| ServiceError::internal(e.to_string()))?),
        }
    }

    fn exec_add(
        &self,
        name: &str,
        private_key: &str,
        force: bool,
        json: bool,
        verbosity: &VerbosityFlags,
    ) -> Result<()> {
        let verbose = verbosity.is_verbose();

        if verbose {
            println!("{} Adding key '{}'...", "Key".blue(), name.yellow());
        }

        // Validate the private key
        let address = match self.validate_private_key(private_key) {
            Ok(addr) => addr,
            Err(e) => {
                let formatter = OutputFormatter::from_flags(json, verbose);
                let key_output = KeyOutput {
                    status: OperationStatus {
                        success: false,
                        message: Some(e.to_string()),
                        code: "KEY_ERROR".to_string(),
                        context: None,
                    },
                    action: "add".to_string(),
                    key_info: Some(json!({"key_name": name})),
                    message: None,
                };

                if json {
                    let formatted = match formatter.format_key(&key_output) {
                        Ok(output) => output,
                        Err(e) => format!("Error formatting output: {}", e),
                    };
                    println!("{}", formatted);
                    return Ok(());
                } else {
                    return Err(e);
                }
            }
        };

        // Load existing keystore
        let mut keystore = self.load_keystore()?;

        // Check if key name already exists
        if keystore.keys.contains_key(name) && !force {
            let formatter = OutputFormatter::from_flags(json, verbose);
            let key_output = KeyOutput {
                status: OperationStatus {
                    success: false,
                    message: Some(format!(
                        "Key '{}' already exists. Use --force to overwrite",
                        name
                    )),
                    code: "KEY_EXISTS".to_string(),
                    context: None,
                },
                action: "add".to_string(),
                key_info: Some(json!({"key_name": name})),
                message: None,
            };

            if json {
                let formatted = match formatter.format_key(&key_output) {
                    Ok(output) => output,
                    Err(e) => format!("Error formatting output: {}", e),
                };
                println!("{}", formatted);
                return Ok(());
            } else {
                return Err(anyhow!(
                    "Key '{}' already exists. Use --force to overwrite",
                    name
                ));
            }
        }

        // Add the key
        let key_data = KeyData {
            private_key: if private_key.starts_with("0x") {
                private_key.to_string()
            } else {
                format!("0x{}", private_key)
            },
            address: address.clone(),
            created_at: get_current_timestamp(),
        };

        keystore.keys.insert(name.to_string(), key_data);

        // If this is the first key, make it active
        if keystore.active_key.is_none() {
            keystore.active_key = Some(name.to_string());

            if verbose {
                println!(
                    "{} Set '{}' as active key (first key added)",
                    "Success".green(),
                    name.yellow()
                );
            }
        }

        // Save keystore
        self.save_keystore(&keystore)?;

        // Update legacy key.json for backward compatibility
        if keystore.active_key.as_ref() == Some(&name.to_string()) {
            self.update_legacy_key_file(&keystore)?;
        }

        // Use unified output formatter
        let formatter = OutputFormatter::from_flags(json, verbose);

        let key_info = json!({
            "name": name,
            "address": address,
            "is_active": keystore.active_key.as_ref() == Some(&name.to_string()),
            "created_at": get_current_timestamp()
        });

        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some(format!("Key '{}' added successfully", name)),
                code: "SUCCESS".to_string(),
                context: None,
            },
            action: "add".to_string(),
            key_info: Some(key_info),
            message: None,
        };

        let formatted = match formatter.format_key(&key_output) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        };
        println!("{}", formatted);

        Ok(())
    }

    fn exec_remove(
        &self,
        name: &str,
        json_output: bool,
        verbosity: &VerbosityFlags,
    ) -> Result<()> {
        let verbose = verbosity.is_verbose();

        // Load keystore
        let mut keystore = self.load_keystore()?;

        // Check if key exists
        if !keystore.keys.contains_key(name) {
            let formatter = OutputFormatter::from_flags(json_output, verbose);
            let key_output = KeyOutput {
                status: OperationStatus {
                    success: false,
                    message: Some(format!("Key '{}' not found", name)),
                    code: "KEY_NOT_FOUND".to_string(),
                    context: None,
                },
                action: "remove".to_string(),
                key_info: Some(json!({"key_name": name})),
                message: None,
            };

            let formatted = match formatter.format_key(&key_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting output: {}", e),
            };
            println!("{}", formatted);
            return Ok(());
        }

        // Check if this is the active key
        let was_active = keystore.active_key.as_ref() == Some(&name.to_string());

        // Remove the key
        keystore.keys.remove(name);

        // If this was the active key, clear active key or set to another key
        if was_active {
            if keystore.keys.is_empty() {
                keystore.active_key = None;
            } else {
                // Set the first available key as active
                let new_active = keystore.keys.keys().next().unwrap().clone();
                keystore.active_key = Some(new_active.clone());
            }
        }

        // Save keystore
        self.save_keystore(&keystore)?;

        // Update legacy key.json
        self.update_legacy_key_file(&keystore)?;

        // Use unified output formatter
        let formatter = OutputFormatter::from_flags(json_output, verbose);

        let key_info = json!({
            "key_name": name,
            "was_active": was_active,
            "new_active_key": keystore.active_key,
            "keys_remaining": keystore.keys.len()
        });

        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some(format!("Key '{}' removed successfully", name)),
                code: "SUCCESS".to_string(),
                context: None,
            },
            action: "remove".to_string(),
            key_info: Some(key_info),
            message: None,
        };

        let formatted = match formatter.format_key(&key_output) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        };
        println!("{}", formatted);

        Ok(())
    }

    fn exec_list(&self, json_output: bool, verbosity: &VerbosityFlags) -> Result<()> {
        let keystore = self.load_keystore()?;
        let verbose = verbosity.is_verbose();

        // Use unified output formatter
        let formatter = OutputFormatter::from_flags(json_output, verbose);

        let mut keys_info = serde_json::Map::new();
        for (name, key_data) in &keystore.keys {
            keys_info.insert(
                name.clone(),
                json!({
                    "address": key_data.address,
                    "created_at": key_data.created_at,
                    "is_active": keystore.active_key.as_ref() == Some(name)
                }),
            );
        }

        let key_info = json!({
            "keys": keys_info,
            "active_key": keystore.active_key,
            "total_keys": keystore.keys.len()
        });

        let message = if keystore.keys.is_empty() {
            Some("No keys found. Use 'cargo vcontract key generate <name>' to create a new key".to_string())
        } else {
            Some(format!("Found {} key(s)", keystore.keys.len()))
        };

        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message,
                code: "SUCCESS".to_string(),
                context: None,
            },
            action: "list".to_string(),
            key_info: Some(key_info),
            message: None,
        };

        let formatted = match formatter.format_key(&key_output) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        };
        println!("{}", formatted);

        Ok(())
    }

    fn exec_use(
        &self,
        name: &str,
        json_output: bool,
        verbosity: &VerbosityFlags,
    ) -> Result<()> {
        let verbose = verbosity.is_verbose();

        // Load keystore
        let mut keystore = self.load_keystore()?;

        // Check if key exists
        if !keystore.keys.contains_key(name) {
            let formatter = OutputFormatter::from_flags(json_output, verbose);
            let key_output = KeyOutput {
                status: OperationStatus {
                    success: false,
                    message: Some(format!("Key '{}' not found. Use 'cargo vcontract key ls' to see available keys", name)),
                    code: "KEY_NOT_FOUND".to_string(),
                    context: None,
                },
                action: "use".to_string(),
                key_info: Some(json!({"key_name": name})),
                message: None,
            };

            let formatted = match formatter.format_key(&key_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting output: {}", e),
            };
            println!("{}", formatted);
            return Ok(());
        }

        // Set as active key
        let previous_active = keystore.active_key.clone();
        keystore.active_key = Some(name.to_string());

        // Save keystore
        self.save_keystore(&keystore)?;

        // Update legacy key.json for backward compatibility
        self.update_legacy_key_file(&keystore)?;

        let key_data = &keystore.keys[name];

        // Use unified output formatter
        let formatter = OutputFormatter::from_flags(json_output, verbose);

        let key_info = json!({
            "key_name": name,
            "address": key_data.address,
            "previous_active": previous_active,
            "was_already_active": previous_active.as_ref() == Some(&name.to_string())
        });

        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message: Some(format!("Now using key '{}'", name)),
                code: "SUCCESS".to_string(),
                context: None,
            },
            action: "use".to_string(),
            key_info: Some(key_info),
            message: None,
        };

        let formatted = match formatter.format_key(&key_output) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        };
        println!("{}", formatted);

        Ok(())
    }

    fn exec_generate(
        &self,
        name: &str,
        seed: Option<&String>,
        activate: bool,
        force: bool,
        json_output: bool,
        verbosity: &VerbosityFlags,
    ) -> Result<()> {
        let verbose = verbosity.is_verbose();

        // Create wallet and generate keypair
        let mut wallet = Wallet::new();

        let keypair = match seed {
            Some(seed_str) => wallet
                .create_keypair_from_seed("generated".to_string(), seed_str.as_bytes())?,
            None => wallet.create_keypair("generated".to_string())?,
        };

        // Get private key and address
        let private_key_hex = format!("0x{}", hex::encode(keypair.private_key()));
        let public_key_hex = format!("0x{}", hex::encode(keypair.public_key()));
        let address_hex = format!("0x{}", hex::encode(keypair.address()));

        // Try to add the key to keystore
        let (added_to_store, was_activated, store_error) =
            match self.add_key_to_store(name, &private_key_hex, force, activate) {
                Ok(was_activated) => (true, was_activated, None),
                Err(e) => (false, false, Some(e.to_string())),
            };

        // Use unified output formatter
        let formatter = OutputFormatter::from_flags(json_output, verbose);

        let mut key_info = json!({
            "name": name,
            "private_key": private_key_hex,
            "public_key": public_key_hex,
            "address": address_hex,
            "seed_used": seed.is_some(),
            "auto_activate": activate,
            "added_to_store": added_to_store,
            "activated": was_activated
        });

        if let Some(error) = store_error.as_ref() {
            key_info["store_error"] = json!(error);
        }

        let message = if added_to_store {
            if was_activated {
                Some(format!("Key '{}' generated and set as active", name))
            } else {
                Some(format!("Key '{}' generated and added to keystore", name))
            }
        } else {
            Some(format!(
                "Key '{}' generated but failed to add to keystore",
                name
            ))
        };

        let key_output = KeyOutput {
            status: OperationStatus {
                success: true, // Generation is successful even if storing fails
                message,
                code: "SUCCESS".to_string(),
                context: None,
            },
            action: "generate".to_string(),
            key_info: Some(key_info),
            message: None,
        };

        let formatted = match formatter.format_key(&key_output) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        };
        println!("{}", formatted);

        Ok(())
    }

    fn exec_status(&self, json_output: bool, verbosity: &VerbosityFlags) -> Result<()> {
        let keystore = self.load_keystore()?;
        let verbose = verbosity.is_verbose();

        // Use unified output formatter
        let formatter = OutputFormatter::from_flags(json_output, verbose);

        let active_key_info = if let Some(active_name) = &keystore.active_key {
            if let Some(key_data) = keystore.keys.get(active_name) {
                json!({
                    "name": active_name,
                    "address": key_data.address,
                    "created_at": key_data.created_at
                })
            } else {
                json!(null)
            }
        } else {
            json!(null)
        };

        let key_info = json!({
            "authenticated": keystore.active_key.is_some(),
            "active_key": active_key_info,
            "total_keys": keystore.keys.len(),
            "keystore_file": self.get_keystore_path()?.display().to_string(),
            "has_invalid_active": keystore.active_key.is_some() &&
                keystore.active_key.as_ref()
                    .map(|name| !keystore.keys.contains_key(name))
                    .unwrap_or(false)
        });

        let message = if keystore.active_key.is_some() {
            if keystore
                .active_key
                .as_ref()
                .map(|name| keystore.keys.contains_key(name))
                .unwrap_or(false)
            {
                Some("Authenticated with active key".to_string())
            } else {
                Some("Warning: Invalid active key reference".to_string())
            }
        } else if keystore.keys.is_empty() {
            Some("No keys found. Use 'cargo vcontract key generate <name>' to create your first key".to_string())
        } else {
            Some("No active key set. Use 'cargo vcontract key use <name>' to activate a key".to_string())
        };

        let key_output = KeyOutput {
            status: OperationStatus {
                success: true,
                message,
                code: "SUCCESS".to_string(),
                context: None,
            },
            action: "status".to_string(),
            key_info: Some(key_info),
            message: None,
        };

        let formatted = match formatter.format_key(&key_output) {
            Ok(output) => output,
            Err(e) => format!("Error formatting output: {}", e),
        };
        println!("{}", formatted);

        Ok(())
    }

    // Helper methods

    fn validate_private_key(&self, private_key: &str) -> Result<String> {
        let mut wallet = Wallet::new();

        // Clean the private key (remove 0x prefix if present)
        let private_key_clean = private_key.trim_start_matches("0x");

        // Convert hex private key to bytes
        let private_key_bytes = hex::decode(private_key_clean)
            .map_err(|e| anyhow!("Invalid private key format: {}", e))?;

        // Validate key length (64 bytes for Schnorr private keys)
        if private_key_bytes.len() != 64 {
            return Err(anyhow!(
                "Invalid private key length: expected 64 bytes, got {}. Schnorr private keys should be 64 bytes (128 hex characters).",
                private_key_bytes.len()
            ));
        }

        // Import private key to validate it
        wallet
            .import_keypair("temp".to_string(), private_key_bytes)
            .map_err(|e| anyhow!("Failed to import private key: {}", e))?;

        // Get address from the wallet
        let keypair = wallet
            .get_default_keypair()
            .ok_or_else(|| anyhow!("Failed to get keypair"))?;

        let address_bytes = keypair.address();
        let address = format!("0x{}", hex::encode(&address_bytes));

        Ok(address)
    }

    fn get_keystore_path(&self) -> Result<std::path::PathBuf> {
        let vgraph_dir = dirs::home_dir()
            .ok_or_else(|| anyhow!("Could not determine home directory"))?
            .join(".vgraph");

        Ok(vgraph_dir.join("keystore.json"))
    }

    fn load_keystore(&self) -> Result<KeyStore> {
        let keystore_path = self.get_keystore_path()?;

        if !keystore_path.exists() {
            return Ok(KeyStore::default());
        }

        let content = fs::read_to_string(&keystore_path)
            .map_err(|e| anyhow!("Failed to read keystore: {}", e))?;

        serde_json::from_str(&content)
            .map_err(|e| anyhow!("Failed to parse keystore: {}", e))
    }

    fn save_keystore(&self, keystore: &KeyStore) -> Result<()> {
        let keystore_path = self.get_keystore_path()?;

        // Create directory if it doesn't exist
        if let Some(parent) = keystore_path.parent() {
            fs::create_dir_all(parent)?;
        }

        let content = serde_json::to_string_pretty(keystore)?;
        fs::write(&keystore_path, content)?;

        // Set appropriate permissions (only owner can read/write)
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let metadata = fs::metadata(&keystore_path)?;
            let mut permissions = metadata.permissions();
            permissions.set_mode(0o600); // Read/write for owner only
            fs::set_permissions(&keystore_path, permissions)?;
        }

        Ok(())
    }

    fn add_key_to_store(
        &self,
        name: &str,
        private_key: &str,
        force: bool,
        activate: bool,
    ) -> Result<bool> {
        // Validate the private key
        let address = self.validate_private_key(private_key)?;

        // Load existing keystore
        let mut keystore = self.load_keystore()?;

        // Check if key name already exists
        if keystore.keys.contains_key(name) && !force {
            return Err(anyhow!(
                "Key '{}' already exists. Use --force to overwrite",
                name
            ));
        }

        // Add the key
        let key_data = KeyData {
            private_key: if private_key.starts_with("0x") {
                private_key.to_string()
            } else {
                format!("0x{}", private_key)
            },
            address: address.clone(),
            created_at: get_current_timestamp(),
        };

        keystore.keys.insert(name.to_string(), key_data);

        // Handle activation
        let was_activated = if activate || keystore.active_key.is_none() {
            keystore.active_key = Some(name.to_string());
            true
        } else {
            false
        };

        // Save keystore
        self.save_keystore(&keystore)?;

        // Update legacy key.json for backward compatibility
        if keystore.active_key.as_ref() == Some(&name.to_string()) {
            self.update_legacy_key_file(&keystore)?;
        }

        Ok(was_activated)
    }

    fn update_legacy_key_file(&self, keystore: &KeyStore) -> Result<()> {
        let legacy_path = get_key_file_path()?;

        if let Some(active_name) = &keystore.active_key {
            if let Some(key_data) = keystore.keys.get(active_name) {
                let legacy_data = json!({
                    "private_key": key_data.private_key,
                    "address": key_data.address
                });

                // Create directory if it doesn't exist
                if let Some(parent) = legacy_path.parent() {
                    fs::create_dir_all(parent)?;
                }

                fs::write(&legacy_path, serde_json::to_string_pretty(&legacy_data)?)?;

                // Set appropriate permissions
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let metadata = fs::metadata(&legacy_path)?;
                    let mut permissions = metadata.permissions();
                    permissions.set_mode(0o600);
                    fs::set_permissions(&legacy_path, permissions)?;
                }
            }
        } else {
            // Remove legacy file if no active key
            if legacy_path.exists() {
                fs::remove_file(&legacy_path)?;
            }
        }

        Ok(())
    }
}

/// Get the path to the legacy key file for backward compatibility
fn get_key_file_path() -> Result<std::path::PathBuf> {
    let vgraph_dir = dirs::home_dir()
        .ok_or_else(|| anyhow!("Could not determine home directory"))?
        .join(".vgraph");

    Ok(vgraph_dir.join("key.json"))
}

/// Get saved private key from active key (for backward compatibility with other modules)
pub fn get_saved_private_key() -> Result<String> {
    let keystore_path = get_keystore_path_pub()?;

    if !keystore_path.exists() {
        return Err(anyhow!(
            "No keystore found. Use 'cargo vcontract key add' to add a key"
        ));
    }

    let content = fs::read_to_string(&keystore_path)
        .map_err(|e| anyhow!("Failed to read keystore: {}", e))?;

    let keystore: KeyStore = serde_json::from_str(&content)
        .map_err(|e| anyhow!("Failed to parse keystore: {}", e))?;

    let active_name = keystore.active_key
        .ok_or_else(|| anyhow!("No active key set. Use 'cargo vcontract key use <name>' to set an active key"))?;

    let key_data = keystore
        .keys
        .get(&active_name)
        .ok_or_else(|| anyhow!("Active key '{}' not found in keystore", active_name))?;

    Ok(key_data.private_key.clone())
}

/// Get saved address from active key (for backward compatibility with other modules)
pub fn get_saved_address() -> Result<String> {
    let keystore_path = get_keystore_path_pub()?;

    if !keystore_path.exists() {
        return Err(anyhow!("No keystore found"));
    }

    let content = fs::read_to_string(&keystore_path)
        .map_err(|e| anyhow!("Failed to read keystore: {}", e))?;

    let keystore: KeyStore = serde_json::from_str(&content)
        .map_err(|e| anyhow!("Failed to parse keystore: {}", e))?;

    let active_name = keystore
        .active_key
        .ok_or_else(|| anyhow!("No active key set"))?;

    let key_data = keystore
        .keys
        .get(&active_name)
        .ok_or_else(|| anyhow!("Active key not found"))?;

    Ok(key_data.address.clone())
}

/// Check if user is logged in (has an active key)
pub fn is_logged_in() -> bool {
    if let Ok(keystore_path) = get_keystore_path_pub() {
        if keystore_path.exists() {
            if let Ok(content) = fs::read_to_string(&keystore_path) {
                if let Ok(keystore) = serde_json::from_str::<KeyStore>(&content) {
                    return keystore.active_key.is_some() && !keystore.keys.is_empty();
                }
            }
        }
    }
    false
}

/// Public version of get_keystore_path for use by other modules
pub fn get_keystore_path_pub() -> Result<std::path::PathBuf> {
    let vgraph_dir = dirs::home_dir()
        .ok_or_else(|| anyhow!("Could not determine home directory"))?
        .join(".vgraph");

    Ok(vgraph_dir.join("keystore.json"))
}
