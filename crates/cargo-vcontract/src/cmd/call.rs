use clap::{Args, Subcommand};
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde::Serialize;
use serde_json::{json, Value};
use std::convert::TryFrom;
use std::path::PathBuf;
use tokio::runtime::Runtime;

use crate::cmd::utils::{
    format_error, list_contract_functions_with_context, wait_for_confirmation,
};
use crate::cmd::CommandOutput;
use crate::output::{models::*, OutputFormatter};
use crate::services::{
    AuthService, ConfigService, MetadataService, NetworkService, ServiceError,
    ServiceResult, ValidationService,
};

/// Call a smart contract function
#[derive(Debug, Args)]
#[clap(name = "call")]
pub struct CallCommand {
    #[clap(subcommand)]
    subcmd: Option<CallSubcommand>,

    /// Contract address (required for execute)
    #[clap(value_parser)]
    contract_address: Option<String>,

    /// Function name to call (required for execute)
    #[clap(value_parser)]
    function_name: Option<String>,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Function arguments (in JSON format)
    #[clap(long, short = 'a', num_args = 0..)]
    args: Vec<String>,

    /// Fuel value
    #[clap(long, default_value = "1000000")]
    fuel: u64,

    /// Dependent transaction hash (execute call on top of this pending transaction)
    #[clap(long)]
    dep_tx: Option<String>,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

#[derive(Debug, Subcommand)]
enum CallSubcommand {
    /// List available atomic functions in a contract
    #[clap(name = "ls")]
    List {
        /// Path to contract file (.contract file)
        #[clap(value_parser)]
        contract_file: Option<PathBuf>,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },
}

impl CommandOutput for CallCommand {
    type Output = CallOutput;

    fn is_json(&self) -> bool {
        self.verbosity.is_json()
    }

    fn is_verbose(&self) -> bool {
        self.verbosity.is_verbose()
    }

    fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new().map_err(|e| {
            ServiceError::internal(format!("Failed to create tokio runtime: {}", e))
        })?;
        runtime.block_on(self.async_exec())
    }

    fn format_output<T: Serialize>(&self, data: T) -> String {
        let formatter =
            OutputFormatter::from_flags(self.is_json(), self.verbosity.is_verbose());

        // Try to convert the data to JSON Value first
        let value = serde_json::to_value(&data).unwrap_or_default();

        // If the value matches the structure of CallOutput, convert it
        if let Ok(call_output) = serde_json::from_value::<CallOutput>(value.clone()) {
            match formatter.format_call(&call_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting output: {}", e),
            }
        } else {
            // Otherwise use generic formatting
            let generic_output = GenericOutput {
                status: OperationStatus {
                    success: true,
                    message: None,
                    code: "SUCCESS".to_string(),
                    context: None,
                },
                data: Some(value),
            };

            match formatter.format_generic(&generic_output) {
                Ok(output) => output,
                Err(e) => format!("Error formatting output: {}", e),
            }
        }
    }
}

impl CallCommand {
    async fn async_exec(&self) -> ServiceResult<()> {
        // Initialize services
        let config_service = ConfigService::new();
        let validation_service = ValidationService::new();
        let metadata_service = MetadataService::new();
        let network_service = NetworkService::new();
        let auth_service = AuthService::new();

        // Parse verbosity settings
        let verbosity = contract_build::Verbosity::try_from(&self.verbosity)
            .map_err(|e| ServiceError::internal(e.to_string()))?;
        let verbose = verbosity.is_verbose();
        let is_json = verbosity.is_json();

        // Handle subcommands
        if let Some(subcmd) = &self.subcmd {
            return match subcmd {
                CallSubcommand::List {
                    contract_file,
                    verbosity: sub_verbosity,
                } => {
                    let sub_verbosity =
                        match contract_build::Verbosity::try_from(sub_verbosity) {
                            Ok(v) => v,
                            Err(e) => {
                                let error_msg = e.to_string();
                                if sub_verbosity.is_json() {
                                    println!("{}", format_error(&error_msg, true));
                                    return Ok(());
                                } else {
                                    return Err(ServiceError::internal(e.to_string()));
                                }
                            }
                        };
                    let sub_verbose = sub_verbosity.is_verbose();

                    let contract_file = match contract_file {
                        Some(path) => path.clone(),
                        None => {
                            match metadata_service
                                .find_contract_file_for_deploy_verbose(sub_verbose)
                            {
                                Ok(path) => path,
                                Err(e) => {
                                    return Err(ServiceError::internal(e.to_string()))
                                }
                            }
                        }
                    };
                    Ok(list_contract_functions_with_context(
                        &contract_file,
                        sub_verbose,
                        "call",
                        sub_verbosity.is_json(),
                    )
                    .map_err(|e| ServiceError::internal(e.to_string()))?)
                }
            };
        }

        // Step 1: Validate required parameters
        let contract_address = self.contract_address.as_ref()
            .ok_or_else(|| ServiceError::validation(
                "Contract address is required. Usage: cargo vcontract call <contract_address> <function_name>".to_string()
            ))?;

        let function_name = self.function_name.as_ref()
            .ok_or_else(|| ServiceError::validation(
                "Function name is required. Usage: cargo vcontract call <contract_address> <function_name>".to_string()
            ))?;

        // Step 2: Validate fuel parameter
        validation_service.validate_fuel(self.fuel)?;

        // Step 3: Get node URL from configuration
        let node_url = config_service
            .get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;

        // Step 4: Connect to network
        let client = network_service.connect_to_node(&node_url).await?;

        // Step 5: Parse and validate arguments
        let args = {
            use crate::cmd::utils::ContractDiscoveryService;
            match ContractDiscoveryService::find_for_call(self.verbosity.is_verbose()) {
                Ok(contract_file) => {
                    // Set current contract address for parameter display
                    std::env::set_var("VCONTRACT_CURRENT_ADDRESS", &contract_address);

                    validation_service.validate_types(
                        &self.args,
                        &contract_file,
                        false,
                        Some(function_name),
                        is_json,
                    )?
                }
                Err(_) => {
                    // If no contract file found, use basic JSON validation
                    validation_service.validate_json_args(&self.args)?
                }
            }
        };

        // Step 6: Get authentication credentials
        let private_key = auth_service
            .get_saved_private_key()
            .map_err(|e| ServiceError::auth(e.to_string()))?;

        // Step 7: Build and submit transaction
        if verbose && !is_json {
            println!("{}", "Building contract call transaction...".blue());
            println!(
                "{} Using fuel: {}",
                "[FUEL]".yellow(),
                self.fuel.to_string().green()
            );
        }

        let params = json!([{
            "contract_address": contract_address,
            "function_name": function_name,
            "args": args,
            "dependent_transaction_hash": self.dep_tx.as_deref().unwrap_or(""),
            "fuel": self.fuel,
            "privatekey": private_key
        }]);

        let response = client
            .request("contract.execute", Some(params))
            .await
            .map_err(|e| ServiceError::network(format!("RPC request failed: {}", e)))?;

        let tx_hash = response
            .get("transaction_hash")
            .and_then(|v| v.as_str())
            .ok_or_else(|| {
                ServiceError::network(format!(
                    "Invalid transaction hash response. Full response: {}",
                    response
                ))
            })?;

        // Step 8: Wait for transaction confirmation
        let receipt = wait_for_confirmation(&client, tx_hash, !is_json)
            .await
            .map_err(|e| {
                ServiceError::network(format!("Failed to wait for confirmation: {}", e))
            })?;

        // Step 9: Process and display results
        let status_bool = receipt
            .get("receipt")
            .and_then(|r| r.get("status"))
            .and_then(Value::as_bool)
            .unwrap_or(false);

        let call_result = receipt
            .get("receipt")
            .and_then(|r| r.get("op_result"))
            .and_then(|op| op.get("return_data"))
            .cloned()
            .unwrap_or(json!(null));

        let fuel_consumed = receipt
            .get("receipt")
            .and_then(|r| r.get("logs"))
            .and_then(|logs| logs.as_array())
            .and_then(|logs| {
                for log in logs {
                    if let Some(log_str) = log.as_str() {
                        if log_str.contains("Fuel consumed:") {
                            if let Some(fuel_part) =
                                log_str.split("Fuel consumed:").nth(1)
                            {
                                if let Ok(fuel_val) = fuel_part.trim().parse::<u64>() {
                                    return Some(fuel_val);
                                }
                            }
                        }
                    }
                }
                None
            })
            .unwrap_or(0);

        let success = status_bool;

        // Create CallOutput using our new output structure
        let call_output = CallOutput {
            status: OperationStatus {
                success,
                message: if success {
                    Some("Contract call successful".to_string())
                } else {
                    Some("Contract call failed".to_string())
                },
                code: if success {
                    "SUCCESS".to_string()
                } else {
                    "CALL_FAILED".to_string()
                },
                context: None,
            },
            transaction_hash: tx_hash.to_string(),
            function_name: function_name.to_string(),
            result: if success && call_result != json!(null) {
                Some(call_result)
            } else {
                None
            },
            fuel_consumed: if fuel_consumed > 0 {
                Some(fuel_consumed)
            } else {
                None
            },
            receipt: if verbose { Some(receipt) } else { None },
        };

        // Use our new format_output method
        println!("{}", self.format_output(call_output));

        Ok(())
    }
}
