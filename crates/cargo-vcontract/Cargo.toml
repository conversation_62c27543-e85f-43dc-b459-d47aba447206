[package]
name = "cargo-vcontract"
version = "0.1.2"
edition = "2021"

[[bin]]
name = "cargo-vcontract"
path = "src/main.rs"

[dependencies]
contract-build = { version = "0.1.0", path = "../build" }

clap = { version = "4.5.1", features = ["derive", "env"] }
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
anyhow = "1.0.79"
colored = "3.0.0"
thiserror = "2.0"
vgraph-sdk-core = { git = "ssh://**************/virtualeconomy/vgraph-sdk.git", rev = "9afc8091f296540f459bdcd41eb156e76cb2a50f" }
tokio = { version = "1.36", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.114"
hex = "0.4.3"
dirs = "6.0.0"
glob = "0.3.1"
regex = "1.10.3"

[dev-dependencies]
tempfile = "3.0"
tokio-test = "0.4"
serial_test = "2.0"
